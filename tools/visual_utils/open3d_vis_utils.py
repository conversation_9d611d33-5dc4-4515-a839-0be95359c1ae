"""
Open3d visualization tool box
Written by <PERSON><PERSON>
All rights preserved from 2021 - present.
"""
import open3d
import torch
import matplotlib
import numpy as np

# box_colormap = [
#     [1, 1, 1],
#     [0, 1, 0],
#     [0, 1, 1],
#     [1, 1, 0],
# ]

box_colormap = [
    [1, 1, 1],    # 0: 白
    [0, 1, 0],    # 1: 绿
    [0, 0, 1],    # 2: 蓝
    [1, 0, 0],    # 3: 红
    [1, 1, 0],    # 4: 黄
    [0, 1, 1],    # 5: 青
    [1, 0, 1],    # 6: 品红
    [0.5, 0.5, 0],# 7: 橄榄
    [0.5, 0, 0.5],# 8: 紫
    [0, 0.5, 0.5],# 9: 深青
    [0.3, 0.3, 0.3], # 10: 灰
    # [0.6, 0.2, 0.2], # 11
    # [0.2, 0.6, 0.2], # 12
    # [0.2, 0.2, 0.6], # 13
    # ...
]

def get_coor_colors(obj_labels):
    """
    Args:
        obj_labels: 1 is ground, labels > 1 indicates different instance cluster

    Returns:
        rgb: [N, 3]. color for each point.
    """
    colors = matplotlib.colors.XKCD_COLORS.values()
    max_color_num = obj_labels.max()

    color_list = list(colors)[:max_color_num+1]
    colors_rgba = [matplotlib.colors.to_rgba_array(color) for color in color_list]
    label_rgba = np.array(colors_rgba)[obj_labels]
    label_rgba = label_rgba.squeeze()[:, :3]

    return label_rgba


def draw_scenes(points, gt_boxes=None, ref_boxes=None, ref_labels=None, ref_scores=None, point_colors=None, draw_origin=True):
    # Convert tensors to numpy arrays
    if isinstance(points, torch.Tensor):
        points = points.cpu().numpy()
    if isinstance(gt_boxes, torch.Tensor):
        gt_boxes = gt_boxes.cpu().numpy()
    if isinstance(ref_boxes, torch.Tensor):
        ref_boxes = ref_boxes.cpu().numpy()
    if isinstance(ref_labels, torch.Tensor):
        ref_labels = ref_labels.cpu().numpy()
    if isinstance(ref_scores, torch.Tensor):
        ref_scores = ref_scores.cpu().numpy()

    # Debug information
    print(f"Points shape: {points.shape}")
    if ref_boxes is not None:
        print(f"Ref boxes shape: {ref_boxes.shape}")
        print(f"Box center range - X: [{ref_boxes[:, 0].min():.2f}, {ref_boxes[:, 0].max():.2f}]")
        print(f"Box center range - Y: [{ref_boxes[:, 1].min():.2f}, {ref_boxes[:, 1].max():.2f}]")
        print(f"Box center range - Z: [{ref_boxes[:, 2].min():.2f}, {ref_boxes[:, 2].max():.2f}]")

    print(f"Point cloud range - X: [{points[:, 0].min():.2f}, {points[:, 0].max():.2f}]")
    print(f"Point cloud range - Y: [{points[:, 1].min():.2f}, {points[:, 1].max():.2f}]")
    print(f"Point cloud range - Z: [{points[:, 2].min():.2f}, {points[:, 2].max():.2f}]")

    vis = open3d.visualization.Visualizer()
    vis.create_window()

    vis.get_render_option().point_size = 1.0
    vis.get_render_option().background_color = np.zeros(3)

    # draw origin
    if draw_origin:
        axis_pcd = open3d.geometry.TriangleMesh.create_coordinate_frame(size=1.0, origin=[0, 0, 0])
        vis.add_geometry(axis_pcd)

    # Ensure points have the right shape
    if points.shape[1] < 3:
        raise ValueError(f"Points must have at least 3 dimensions, got {points.shape[1]}")

    pts = open3d.geometry.PointCloud()
    pts.points = open3d.utility.Vector3dVector(points[:, :3])

    vis.add_geometry(pts)
    if point_colors is None:
        pts.colors = open3d.utility.Vector3dVector(np.ones((points.shape[0], 3)))
    else:
        pts.colors = open3d.utility.Vector3dVector(point_colors)

    if gt_boxes is not None and len(gt_boxes) > 0:
        vis = draw_box(vis, gt_boxes, (0, 0, 1))

    if ref_boxes is not None and len(ref_boxes) > 0:
        vis = draw_box(vis, ref_boxes, (0, 1, 0), ref_labels, ref_scores)

    vis.run()
    vis.destroy_window()


def translate_boxes_to_open3d_instance(gt_boxes):
    """
    Convert box format to Open3D format
    Box format: [x, y, z, dx, dy, dz, heading]

    Open3D box corners:
             4-------- 6
           /|         /|
          5 -------- 3 .
          | |        | |
          . 7 -------- 1
          |/         |/
          2 -------- 0
    """
    center = gt_boxes[0:3]
    lwh = gt_boxes[3:6]

    # Handle heading angle - ensure it's in the correct coordinate system
    heading = gt_boxes[6]

    # For nuScenes data, the heading might need adjustment
    # The axis_angles represents rotation around z-axis
    axis_angles = np.array([0, 0, heading + 1e-10])
    rot = open3d.geometry.get_rotation_matrix_from_axis_angle(axis_angles)

    # Create oriented bounding box
    box3d = open3d.geometry.OrientedBoundingBox(center, rot, lwh)

    line_set = open3d.geometry.LineSet.create_from_oriented_bounding_box(box3d)

    # Add additional lines for better visualization
    lines = np.asarray(line_set.lines)
    lines = np.concatenate([lines, np.array([[1, 4], [7, 6]])], axis=0)

    line_set.lines = open3d.utility.Vector2iVector(lines)

    return line_set, box3d


def draw_box(vis, gt_boxes, color=(0, 1, 0), ref_labels=None, score=None):
    for i in range(gt_boxes.shape[0]):
        line_set, box3d = translate_boxes_to_open3d_instance(gt_boxes[i])
        if ref_labels is None:
            line_set.paint_uniform_color(color)
        else:
            line_set.paint_uniform_color(box_colormap[ref_labels[i]])

        vis.add_geometry(line_set)

        # if score is not None:
        #     corners = box3d.get_box_points()
        #     vis.add_3d_label(corners[5], '%.2f' % score[i])
    return vis
