import argparse
import pickle
from pathlib import Path

try:
    import open3d
    from visual_utils import open3d_vis_utils as V
    OPEN3D_FLAG = True
except:
    import mayavi.mlab as mlab
    from visual_utils import visualize_utils as V
    OPEN3D_FLAG = False

import numpy as np
import torch

from pcdet.config import cfg, cfg_from_yaml_file
from pcdet.datasets import DatasetTemplate
from pcdet.models import build_network, load_data_to_gpu
from pcdet.utils import common_utils


class CleanDemo(DatasetTemplate):
    def __init__(self, dataset_cfg, class_names, info_path, training=False, root_path=None, logger=None):
        """
        清洁版本的demo，专注于显示高质量的检测结果
        """
        super().__init__(
            dataset_cfg=dataset_cfg, class_names=class_names, training=training, root_path=root_path, logger=logger
        )
        
        # 直接加载info文件
        with open(info_path, 'rb') as f:
            self.infos = pickle.load(f)
        
        logger.info(f'Loaded {len(self.infos)} samples from {info_path}')

    def __len__(self):
        return len(self.infos)

    def get_lidar_with_sweeps(self, index, max_sweeps=10):
        """
        完全按照nuScenes数据集的方式处理多帧数据
        """
        info = self.infos[index]
        lidar_path = self.root_path / info['lidar_path']
        
        # 读取主帧点云数据
        points = np.fromfile(str(lidar_path), dtype=np.float32, count=-1).reshape([-1, 5])[:, :4]
        
        sweep_points_list = [points]
        sweep_times_list = [np.zeros((points.shape[0], 1))]
        
        # 处理sweep数据（多帧融合）
        if 'sweeps' in info and len(info['sweeps']) > 0:
            num_sweeps = min(max_sweeps - 1, len(info['sweeps']))
            for k in range(num_sweeps):
                sweep_info = info['sweeps'][k]
                sweep_lidar_path = self.root_path / sweep_info['lidar_path']
                
                if sweep_lidar_path.exists():
                    points_sweep = np.fromfile(str(sweep_lidar_path), dtype=np.float32, count=-1).reshape([-1, 5])[:, :4]
                    
                    # 移除ego附近的点
                    def remove_ego_points(points, center_radius=1.0):
                        mask = ~((np.abs(points[:, 0]) < center_radius) & (np.abs(points[:, 1]) < center_radius))
                        return points[mask]
                    
                    points_sweep = remove_ego_points(points_sweep)
                    
                    # 应用变换矩阵
                    if 'transform_matrix' in sweep_info and sweep_info['transform_matrix'] is not None:
                        points_sweep = points_sweep.T  # (4, N)
                        num_points = points_sweep.shape[1]
                        points_sweep[:3, :] = sweep_info['transform_matrix'].dot(
                            np.vstack((points_sweep[:3, :], np.ones(num_points)))
                        )[:3, :]
                        points_sweep = points_sweep.T  # (N, 4)
                    
                    sweep_points_list.append(points_sweep)
                    
                    # 时间戳
                    time_lag = sweep_info.get('time_lag', 0.0)
                    times_sweep = time_lag * np.ones((points_sweep.shape[0], 1))
                    sweep_times_list.append(times_sweep)
        
        # 合并所有点云
        points = np.concatenate(sweep_points_list, axis=0)
        times = np.concatenate(sweep_times_list, axis=0).astype(points.dtype)
        
        # 添加时间戳维度
        points = np.concatenate((points, times), axis=1)
        return points

    def __getitem__(self, index):
        info = self.infos[index]
        
        # 使用多帧融合获取点云
        points = self.get_lidar_with_sweeps(index, max_sweeps=self.dataset_cfg.MAX_SWEEPS)
        
        print(f"Sample {index}: {Path(info['lidar_path']).name}")
        print(f"Original points shape: {points.shape}")

        input_dict = {
            'points': points,
            'frame_id': Path(info['lidar_path']).stem,
            'metadata': {'token': info['token']}
        }

        data_dict = self.prepare_data(data_dict=input_dict)
        return data_dict


def filter_predictions_advanced(pred_boxes, pred_scores, pred_labels, score_thresh=0.4, max_boxes=50):
    """
    高级预测过滤，移除明显错误的检测
    """
    # 1. 基本分数过滤
    score_mask = pred_scores > score_thresh
    
    # 2. 移除异常大小的框
    if len(pred_boxes) > 0:
        # 检查框的尺寸是否合理
        dx, dy, dz = pred_boxes[:, 3], pred_boxes[:, 4], pred_boxes[:, 5]
        size_mask = (
            (dx > 0.5) & (dx < 20) &  # 长度合理
            (dy > 0.5) & (dy < 10) &  # 宽度合理
            (dz > 0.5) & (dz < 5)     # 高度合理
        )
    else:
        size_mask = np.ones(len(pred_boxes), dtype=bool)
    
    # 3. 移除在边界附近的框（可能是边界效应）
    if len(pred_boxes) > 0:
        boundary_margin = 5.0  # 距离边界5米
        boundary_mask = (
            (np.abs(pred_boxes[:, 0]) < (51.2 - boundary_margin)) &
            (np.abs(pred_boxes[:, 1]) < (51.2 - boundary_margin))
        )
    else:
        boundary_mask = np.ones(len(pred_boxes), dtype=bool)
    
    # 4. 组合所有过滤条件
    final_mask = score_mask & size_mask & boundary_mask
    
    filtered_boxes = pred_boxes[final_mask]
    filtered_scores = pred_scores[final_mask]
    filtered_labels = pred_labels[final_mask]
    
    # 5. 按分数排序，只保留前N个最高分的
    if len(filtered_boxes) > max_boxes:
        top_indices = np.argsort(filtered_scores)[-max_boxes:]
        filtered_boxes = filtered_boxes[top_indices]
        filtered_scores = filtered_scores[top_indices]
        filtered_labels = filtered_labels[top_indices]
    
    return filtered_boxes, filtered_scores, filtered_labels, {
        'score_filtered': score_mask.sum(),
        'size_filtered': size_mask.sum(),
        'boundary_filtered': boundary_mask.sum(),
        'final_count': len(filtered_boxes)
    }


def parse_config():
    parser = argparse.ArgumentParser(description='Clean nuScenes Demo')
    parser.add_argument('--cfg_file', type=str, required=True,
                        help='specify the config for demo')
    parser.add_argument('--data_root', type=str, required=True,
                        help='specify the root path of nuScenes dataset')
    parser.add_argument('--info_path', type=str, required=True,
                        help='specify the info file path')
    parser.add_argument('--ckpt', type=str, required=True, 
                        help='specify the pretrained model')
    parser.add_argument('--score_thresh', type=float, default=0.4,
                        help='score threshold for filtering predictions')
    parser.add_argument('--max_boxes', type=int, default=30,
                        help='maximum number of boxes to display')
    parser.add_argument('--max_samples', type=int, default=5,
                        help='maximum number of samples to visualize')

    args = parser.parse_args()
    cfg_from_yaml_file(args.cfg_file, cfg)
    return args, cfg


def main():
    args, cfg = parse_config()
    logger = common_utils.create_logger()
    logger.info('-----------------Clean nuScenes Demo-------------------------')
    
    demo_dataset = CleanDemo(
        dataset_cfg=cfg.DATA_CONFIG, 
        class_names=cfg.CLASS_NAMES, 
        info_path=args.info_path,
        training=False,
        root_path=Path(args.data_root), 
        logger=logger
    )
    
    logger.info(f'Using MAX_SWEEPS: {cfg.DATA_CONFIG.MAX_SWEEPS}')
    logger.info(f'Point cloud range: {cfg.DATA_CONFIG.POINT_CLOUD_RANGE}')
    logger.info(f'Score threshold: {args.score_thresh}')
    logger.info(f'Max boxes per sample: {args.max_boxes}')

    model = build_network(model_cfg=cfg.MODEL, num_class=len(cfg.CLASS_NAMES), dataset=demo_dataset)
    model.load_params_from_file(filename=args.ckpt, logger=logger, to_cpu=True)
    model.cuda()
    model.eval()
    
    with torch.no_grad():
        max_samples = min(args.max_samples, len(demo_dataset))
        for idx in range(max_samples):
            logger.info(f'Visualized sample index: \t{idx + 1}/{max_samples}')
            
            try:
                data_dict = demo_dataset[idx]
                data_dict = demo_dataset.collate_batch([data_dict])
                load_data_to_gpu(data_dict)
                
                pred_dicts, _ = model.forward(data_dict)
                
                # 提取预测结果
                pred_boxes = pred_dicts[0]['pred_boxes'].cpu().numpy()
                pred_scores = pred_dicts[0]['pred_scores'].cpu().numpy()
                pred_labels = pred_dicts[0]['pred_labels'].cpu().numpy()
                
                logger.info(f'Raw predictions: {len(pred_boxes)} boxes')
                logger.info(f'Score range: [{pred_scores.min():.3f}, {pred_scores.max():.3f}]')
                
                # 高级过滤
                filtered_boxes, filtered_scores, filtered_labels, filter_stats = filter_predictions_advanced(
                    pred_boxes, pred_scores, pred_labels, 
                    score_thresh=args.score_thresh, 
                    max_boxes=args.max_boxes
                )
                
                logger.info(f'Filtering results:')
                logger.info(f'  Score > {args.score_thresh}: {filter_stats["score_filtered"]} boxes')
                logger.info(f'  Size reasonable: {filter_stats["size_filtered"]} boxes')
                logger.info(f'  Not on boundary: {filter_stats["boundary_filtered"]} boxes')
                logger.info(f'  Final count: {filter_stats["final_count"]} boxes')
                
                if len(filtered_boxes) > 0:
                    logger.info(f'Final score range: [{filtered_scores.min():.3f}, {filtered_scores.max():.3f}]')
                    logger.info(f'Final box center range:')
                    logger.info(f'  X: [{filtered_boxes[:, 0].min():.2f}, {filtered_boxes[:, 0].max():.2f}]')
                    logger.info(f'  Y: [{filtered_boxes[:, 1].min():.2f}, {filtered_boxes[:, 1].max():.2f}]')
                    logger.info(f'  Z: [{filtered_boxes[:, 2].min():.2f}, {filtered_boxes[:, 2].max():.2f}]')
                    
                    # 显示类别分布
                    unique_labels, counts = np.unique(filtered_labels, return_counts=True)
                    for label, count in zip(unique_labels, counts):
                        class_name = cfg.CLASS_NAMES[int(label) - 1]
                        logger.info(f'  {class_name}: {count} boxes')
                
                # 提取点云数据用于可视化
                points = data_dict['points']
                if points.dim() == 2:  # (N, features)
                    points_vis = points[:, 1:4].cpu().numpy()
                else:
                    points_vis = points[0, :, 1:4].cpu().numpy()
                
                logger.info(f'Points for visualization: {points_vis.shape}')
                
                # 只显示检测框，不显示GT
                if len(filtered_boxes) > 0:
                    V.draw_scenes(
                        points=points_vis, 
                        ref_boxes=filtered_boxes[:, :7],
                        ref_scores=filtered_scores, 
                        ref_labels=filtered_labels
                    )
                else:
                    logger.warning("No valid predictions to visualize!")
                    V.draw_scenes(points=points_vis)
                
                if not OPEN3D_FLAG:
                    mlab.show(stop=True)
                else:
                    input("Press Enter to continue to next sample...")
                    
            except Exception as e:
                logger.error(f"Error processing sample {idx}: {e}")
                import traceback
                traceback.print_exc()
                continue

    logger.info('Demo done.')


if __name__ == '__main__':
    main()
