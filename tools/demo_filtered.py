import argparse
import glob
from pathlib import Path

try:
    import open3d
    from visual_utils import open3d_vis_utils as V
    OPEN3D_FLAG = True
except:
    import mayavi.mlab as mlab
    from visual_utils import visualize_utils as V
    OPEN3D_FLAG = False

import numpy as np
import torch

from pcdet.config import cfg, cfg_from_yaml_file
from pcdet.datasets import DatasetTemplate
from pcdet.models import build_network, load_data_to_gpu
from pcdet.utils import common_utils


class FilteredDemoDataset(DatasetTemplate):
    def __init__(self, dataset_cfg, class_names, training=False, root_path=None, logger=None, ext='.bin'):
        super().__init__(
            dataset_cfg=dataset_cfg, class_names=class_names, training=training, root_path=root_path, logger=logger
        )
        self.ext = ext
        data_file_list = glob.glob(str(root_path / f'*{self.ext}')) if self.root_path.is_dir() else [self.root_path]
        data_file_list.sort()
        self.sample_file_list = data_file_list

    def __len__(self):
        return len(self.sample_file_list)

    def __getitem__(self, index):
        if self.ext == '.bin':
            points = np.fromfile(self.sample_file_list[index], dtype=np.float32)
            if points.shape[0] % 5 == 0:  # nuScenes format (5 dimensions)
                points = points.reshape(-1, 5)
                # 按照nuScenes数据集的处理方式：取前4维，然后添加时间戳
                points_4d = points[:, :4]  # x, y, z, intensity
                timestamp = np.zeros((points_4d.shape[0], 1), dtype=np.float32)  # 时间戳为0
                points = np.concatenate((points_4d, timestamp), axis=1)  # 重新组合为5维
                print(f"Loaded nuScenes format: {points_4d.shape[0]} points, processed as 5D")
            elif points.shape[0] % 4 == 0:  # KITTI format (4 dimensions)
                points = points.reshape(-1, 4)
                expected_features = len(self.dataset_cfg.POINT_FEATURE_ENCODING.src_feature_list)
                if expected_features == 5:
                    timestamp = np.zeros((points.shape[0], 1), dtype=np.float32)
                    points = np.concatenate((points, timestamp), axis=1)
                    print(f"Loaded KITTI format: {points.shape[0]} points, added timestamp for compatibility")
                else:
                    print(f"Loaded KITTI format: {points.shape[0]} points with 4D")
            else:
                raise ValueError(f"Unexpected point cloud format with {points.shape[0]} total elements")
        elif self.ext == '.npy':
            points = np.load(self.sample_file_list[index])
            expected_features = len(self.dataset_cfg.POINT_FEATURE_ENCODING.src_feature_list)
            if points.shape[1] > expected_features:
                points = points[:, :expected_features]
            elif points.shape[1] < expected_features:
                missing_features = expected_features - points.shape[1]
                padding = np.zeros((points.shape[0], missing_features), dtype=np.float32)
                points = np.concatenate([points, padding], axis=1)
        else:
            raise NotImplementedError

        print(f"Original points shape: {points.shape}")
        print(f"Original point cloud range:")
        print(f"  X: [{points[:, 0].min():.2f}, {points[:, 0].max():.2f}]")
        print(f"  Y: [{points[:, 1].min():.2f}, {points[:, 1].max():.2f}]")
        print(f"  Z: [{points[:, 2].min():.2f}, {points[:, 2].max():.2f}]")

        input_dict = {
            'points': points,
            'frame_id': index,
        }

        data_dict = self.prepare_data(data_dict=input_dict)
        return data_dict


def parse_config():
    parser = argparse.ArgumentParser(description='Filtered Demo for OpenPCDet')
    parser.add_argument('--cfg_file', type=str, required=True,
                        help='specify the config for demo')
    parser.add_argument('--data_path', type=str, required=True,
                        help='specify the point cloud data file or directory')
    parser.add_argument('--ckpt', type=str, required=True, 
                        help='specify the pretrained model')
    parser.add_argument('--ext', type=str, default='.bin', 
                        help='specify the extension of your point cloud data file')
    parser.add_argument('--score_thresh', type=float, default=0.5,
                        help='score threshold for filtering predictions')
    parser.add_argument('--max_samples', type=int, default=5,
                        help='maximum number of samples to visualize')

    args = parser.parse_args()
    cfg_from_yaml_file(args.cfg_file, cfg)
    return args, cfg


def main():
    args, cfg = parse_config()
    logger = common_utils.create_logger()
    logger.info('-----------------Filtered Demo of OpenPCDet-------------------------')
    
    demo_dataset = FilteredDemoDataset(
        dataset_cfg=cfg.DATA_CONFIG, class_names=cfg.CLASS_NAMES, training=False,
        root_path=Path(args.data_path), ext=args.ext, logger=logger
    )
    logger.info(f'Total number of samples: \t{len(demo_dataset)}')

    model = build_network(model_cfg=cfg.MODEL, num_class=len(cfg.CLASS_NAMES), dataset=demo_dataset)
    model.load_params_from_file(filename=args.ckpt, logger=logger, to_cpu=True)
    model.cuda()
    model.eval()
    
    with torch.no_grad():
        max_samples = min(args.max_samples, len(demo_dataset))
        for idx in range(max_samples):
            logger.info(f'Visualized sample index: \t{idx + 1}/{max_samples}')
            
            try:
                data_dict = demo_dataset[idx]
                data_dict = demo_dataset.collate_batch([data_dict])
                load_data_to_gpu(data_dict)
                
                pred_dicts, _ = model.forward(data_dict)
                
                # 提取预测结果
                pred_boxes = pred_dicts[0]['pred_boxes'].cpu().numpy()
                pred_scores = pred_dicts[0]['pred_scores'].cpu().numpy()
                pred_labels = pred_dicts[0]['pred_labels'].cpu().numpy()
                
                logger.info(f'Raw predictions: {len(pred_boxes)} boxes')
                logger.info(f'Score range: [{pred_scores.min():.3f}, {pred_scores.max():.3f}]')
                
                # 应用更严格的过滤
                # 1. 分数阈值过滤
                score_mask = pred_scores > args.score_thresh
                
                # 2. 移除明显异常的预测（可选）
                # 检查预测框是否在合理范围内
                valid_range_mask = (
                    (np.abs(pred_boxes[:, 0]) < 100) &  # X坐标在合理范围内
                    (np.abs(pred_boxes[:, 1]) < 100) &  # Y坐标在合理范围内
                    (pred_boxes[:, 2] > -5) &           # Z坐标不能太低
                    (pred_boxes[:, 2] < 10)             # Z坐标不能太高
                )
                
                # 3. 组合过滤条件
                final_mask = score_mask & valid_range_mask
                
                pred_boxes = pred_boxes[final_mask]
                pred_scores = pred_scores[final_mask]
                pred_labels = pred_labels[final_mask]
                
                logger.info(f'After score filtering (>{args.score_thresh}): {score_mask.sum()} boxes')
                logger.info(f'After range filtering: {valid_range_mask.sum()} boxes')
                logger.info(f'Final filtered predictions: {len(pred_boxes)} boxes')
                
                if len(pred_boxes) > 0:
                    logger.info(f'Filtered score range: [{pred_scores.min():.3f}, {pred_scores.max():.3f}]')
                    logger.info(f'Box center range:')
                    logger.info(f'  X: [{pred_boxes[:, 0].min():.2f}, {pred_boxes[:, 0].max():.2f}]')
                    logger.info(f'  Y: [{pred_boxes[:, 1].min():.2f}, {pred_boxes[:, 1].max():.2f}]')
                    logger.info(f'  Z: [{pred_boxes[:, 2].min():.2f}, {pred_boxes[:, 2].max():.2f}]')
                
                # 提取点云数据用于可视化
                points = data_dict['points']
                if points.dim() == 2:  # (N, features)
                    points_vis = points[:, 1:4].cpu().numpy()  # 去掉batch index，保留x,y,z
                else:
                    points_vis = points[0, :, 1:4].cpu().numpy()
                
                logger.info(f'Points for visualization: {points_vis.shape}')
                logger.info(f'Processed point cloud range:')
                logger.info(f'  X: [{points_vis[:, 0].min():.2f}, {points_vis[:, 0].max():.2f}]')
                logger.info(f'  Y: [{points_vis[:, 1].min():.2f}, {points_vis[:, 1].max():.2f}]')
                logger.info(f'  Z: [{points_vis[:, 2].min():.2f}, {points_vis[:, 2].max():.2f}]')
                
                # 可视化
                if len(pred_boxes) > 0:
                    V.draw_scenes(
                        points=points_vis, 
                        ref_boxes=pred_boxes,
                        ref_scores=pred_scores, 
                        ref_labels=pred_labels
                    )
                else:
                    logger.warning("No valid predictions to visualize!")
                    V.draw_scenes(points=points_vis)
                
                if not OPEN3D_FLAG:
                    mlab.show(stop=True)
                else:
                    input("Press Enter to continue to next sample...")
                    
            except Exception as e:
                logger.error(f"Error processing sample {idx}: {e}")
                import traceback
                traceback.print_exc()
                continue

    logger.info('Demo done.')


if __name__ == '__main__':
    main()
