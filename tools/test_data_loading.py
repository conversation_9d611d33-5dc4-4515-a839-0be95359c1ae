import pickle
import numpy as np
from pathlib import Path

# 测试数据加载
def test_info_file():
    info_path = "/home/<USER>/OpenPCDet/data/nuscenes/v1.0-mini/nuscenes_infos_10sweeps_val.pkl"
    
    print(f"Testing info file: {info_path}")
    print(f"File exists: {Path(info_path).exists()}")
    
    if Path(info_path).exists():
        with open(info_path, 'rb') as f:
            infos = pickle.load(f)
        
        print(f"Number of samples in info file: {len(infos)}")
        
        if len(infos) > 0:
            print(f"First sample keys: {infos[0].keys()}")
            print(f"First sample lidar_path: {infos[0].get('lidar_path', 'N/A')}")
            
            # 检查实际的lidar文件是否存在
            if 'lidar_path' in infos[0]:
                data_root = Path("/home/<USER>/OpenPCDet/data/nuscenes/v1.0-mini")
                lidar_file = data_root / infos[0]['lidar_path']
                print(f"Lidar file exists: {lidar_file.exists()}")
                print(f"Full lidar path: {lidar_file}")
                
                if lidar_file.exists():
                    # 尝试读取点云数据
                    points = np.fromfile(str(lidar_file), dtype=np.float32).reshape(-1, 5)
                    print(f"Point cloud shape: {points.shape}")
                    print(f"Point cloud range:")
                    print(f"  X: [{points[:, 0].min():.2f}, {points[:, 0].max():.2f}]")
                    print(f"  Y: [{points[:, 1].min():.2f}, {points[:, 1].max():.2f}]")
                    print(f"  Z: [{points[:, 2].min():.2f}, {points[:, 2].max():.2f}]")

def test_direct_bin_files():
    print("\n" + "="*50)
    print("Testing direct .bin files:")
    
    lidar_dir = Path("/home/<USER>/OpenPCDet/data/nuscenes/v1.0-mini/samples/LIDAR_TOP")
    bin_files = list(lidar_dir.glob("*.bin"))
    
    print(f"Found {len(bin_files)} .bin files")
    
    if len(bin_files) > 0:
        # 测试第一个文件
        test_file = bin_files[0]
        print(f"Testing file: {test_file.name}")
        
        points = np.fromfile(str(test_file), dtype=np.float32).reshape(-1, 5)
        print(f"Point cloud shape: {points.shape}")
        print(f"Point cloud range:")
        print(f"  X: [{points[:, 0].min():.2f}, {points[:, 0].max():.2f}]")
        print(f"  Y: [{points[:, 1].min():.2f}, {points[:, 1].max():.2f}]")
        print(f"  Z: [{points[:, 2].min():.2f}, {points[:, 2].max():.2f}]")

if __name__ == "__main__":
    test_info_file()
    test_direct_bin_files()
