import pickle
import numpy as np
from pathlib import Path

def test_coordinate_system():
    """测试nuScenes的坐标系和GT boxes"""
    
    # 加载info文件
    info_path = "/home/<USER>/OpenPCDet/data/nuscenes/v1.0-mini/nuscenes_infos_10sweeps_val.pkl"
    with open(info_path, 'rb') as f:
        infos = pickle.load(f)
    
    print("="*60)
    print("nuScenes坐标系测试")
    print("="*60)
    
    # 选择第一个样本
    info = infos[0]
    print(f"Sample: {info['lidar_path']}")
    print(f"Token: {info['token']}")
    
    # 加载点云数据
    data_root = Path("/home/<USER>/OpenPCDet/data/nuscenes/v1.0-mini")
    lidar_file = data_root / info['lidar_path']
    points = np.fromfile(str(lidar_file), dtype=np.float32).reshape(-1, 5)[:, :4]
    
    print(f"\n点云数据:")
    print(f"  形状: {points.shape}")
    print(f"  X范围: [{points[:, 0].min():.2f}, {points[:, 0].max():.2f}]")
    print(f"  Y范围: [{points[:, 1].min():.2f}, {points[:, 1].max():.2f}]")
    print(f"  Z范围: [{points[:, 2].min():.2f}, {points[:, 2].max():.2f}]")
    
    # 检查GT boxes
    if 'gt_boxes' in info:
        gt_boxes = info['gt_boxes']
        gt_names = info['gt_names']
        
        print(f"\nGT Boxes:")
        print(f"  数量: {len(gt_boxes)}")
        print(f"  形状: {gt_boxes.shape}")
        print(f"  类别: {gt_names}")
        
        if len(gt_boxes) > 0:
            print(f"  X中心范围: [{gt_boxes[:, 0].min():.2f}, {gt_boxes[:, 0].max():.2f}]")
            print(f"  Y中心范围: [{gt_boxes[:, 1].min():.2f}, {gt_boxes[:, 1].max():.2f}]")
            print(f"  Z中心范围: [{gt_boxes[:, 2].min():.2f}, {gt_boxes[:, 2].max():.2f}]")
            print(f"  尺寸范围:")
            print(f"    长度(dx): [{gt_boxes[:, 3].min():.2f}, {gt_boxes[:, 3].max():.2f}]")
            print(f"    宽度(dy): [{gt_boxes[:, 4].min():.2f}, {gt_boxes[:, 4].max():.2f}]")
            print(f"    高度(dz): [{gt_boxes[:, 5].min():.2f}, {gt_boxes[:, 5].max():.2f}]")
            print(f"  朝向角: [{gt_boxes[:, 6].min():.2f}, {gt_boxes[:, 6].max():.2f}]")
            
            # 详细显示前几个box
            print(f"\n前3个GT boxes详情:")
            for i in range(min(3, len(gt_boxes))):
                box = gt_boxes[i]
                name = gt_names[i]
                print(f"  Box {i} ({name}):")
                print(f"    中心: ({box[0]:.2f}, {box[1]:.2f}, {box[2]:.2f})")
                print(f"    尺寸: ({box[3]:.2f}, {box[4]:.2f}, {box[5]:.2f})")
                print(f"    朝向: {box[6]:.2f}")
                if box.shape[0] > 7:
                    print(f"    速度: ({box[7]:.2f}, {box[8]:.2f})")
    
    # 检查坐标系转换信息
    if 'ref_from_car' in info:
        print(f"\nref_from_car变换矩阵:")
        print(info['ref_from_car'])
    
    if 'car_from_global' in info:
        print(f"\ncar_from_global变换矩阵:")
        print(info['car_from_global'])
    
    # 分析点云和GT boxes的空间关系
    if 'gt_boxes' in info and len(info['gt_boxes']) > 0:
        gt_boxes = info['gt_boxes']
        
        print(f"\n空间关系分析:")
        
        # 检查GT boxes是否在点云范围内
        points_x_range = [points[:, 0].min(), points[:, 0].max()]
        points_y_range = [points[:, 1].min(), points[:, 1].max()]
        points_z_range = [points[:, 2].min(), points[:, 2].max()]
        
        boxes_x_range = [gt_boxes[:, 0].min(), gt_boxes[:, 0].max()]
        boxes_y_range = [gt_boxes[:, 1].min(), gt_boxes[:, 1].max()]
        boxes_z_range = [gt_boxes[:, 2].min(), gt_boxes[:, 2].max()]
        
        print(f"  点云X范围: [{points_x_range[0]:.2f}, {points_x_range[1]:.2f}]")
        print(f"  GT框X范围: [{boxes_x_range[0]:.2f}, {boxes_x_range[1]:.2f}]")
        print(f"  X重叠: {max(points_x_range[0], boxes_x_range[0]) <= min(points_x_range[1], boxes_x_range[1])}")
        
        print(f"  点云Y范围: [{points_y_range[0]:.2f}, {points_y_range[1]:.2f}]")
        print(f"  GT框Y范围: [{boxes_y_range[0]:.2f}, {boxes_y_range[1]:.2f}]")
        print(f"  Y重叠: {max(points_y_range[0], boxes_y_range[0]) <= min(points_y_range[1], boxes_y_range[1])}")
        
        print(f"  点云Z范围: [{points_z_range[0]:.2f}, {points_z_range[1]:.2f}]")
        print(f"  GT框Z范围: [{boxes_z_range[0]:.2f}, {boxes_z_range[1]:.2f}]")
        print(f"  Z重叠: {max(points_z_range[0], boxes_z_range[0]) <= min(points_z_range[1], boxes_z_range[1])}")
        
        # 检查是否有GT boxes在点云范围外
        outside_boxes = 0
        for i, box in enumerate(gt_boxes):
            x, y, z = box[0], box[1], box[2]
            if (x < points_x_range[0] or x > points_x_range[1] or
                y < points_y_range[0] or y > points_y_range[1] or
                z < points_z_range[0] or z > points_z_range[1]):
                outside_boxes += 1
                print(f"  Box {i} ({gt_names[i]}) 在点云范围外: ({x:.2f}, {y:.2f}, {z:.2f})")
        
        print(f"  总共{outside_boxes}个GT boxes在点云范围外")

def test_nuscenes_coordinate_definition():
    """测试nuScenes坐标系定义"""
    print("\n" + "="*60)
    print("nuScenes坐标系定义")
    print("="*60)
    
    print("根据nuScenes官方文档:")
    print("- X轴: 指向车辆前方")
    print("- Y轴: 指向车辆左侧") 
    print("- Z轴: 指向上方")
    print("- 原点: 车辆后轴中心")
    print()
    print("OpenPCDet使用的坐标系:")
    print("- 应该与nuScenes一致")
    print("- 但需要检查数据预处理过程中是否有坐标变换")

if __name__ == "__main__":
    test_coordinate_system()
    test_nuscenes_coordinate_definition()
