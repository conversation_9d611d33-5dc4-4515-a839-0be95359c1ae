import argparse
import glob
from pathlib import Path

try:
    import open3d
    from visual_utils import open3d_vis_utils as V
    OPEN3D_FLAG = True
except:
    import mayavi.mlab as mlab
    from visual_utils import visualize_utils as V
    OPEN3D_FLAG = False

import numpy as np
import torch

from pcdet.config import cfg, cfg_from_yaml_file
from pcdet.datasets import DatasetTemplate
from pcdet.models import build_network, load_data_to_gpu
from pcdet.utils import common_utils


class DemoDataset(DatasetTemplate):
    def __init__(self, dataset_cfg, class_names, training=True, root_path=None, logger=None, ext='.bin'):
        """
        Args:
            root_path:
            dataset_cfg:
            class_names:
            training:
            logger:
        """
        super().__init__(
            dataset_cfg=dataset_cfg, class_names=class_names, training=training, root_path=root_path, logger=logger
        )
        self.root_path = root_path
        self.ext = ext
        data_file_list = glob.glob(str(root_path / f'*{self.ext}')) if self.root_path.is_dir() else [self.root_path]

        data_file_list.sort()
        self.sample_file_list = data_file_list

    def __len__(self):
        return len(self.sample_file_list)

    def __getitem__(self, index):
        if self.ext == '.bin':
            points = np.fromfile(self.sample_file_list[index], dtype=np.float32)
            # Handle different point cloud formats
            if points.shape[0] % 5 == 0:  # nuScenes format (5 dimensions)
                points = points.reshape(-1, 5)[:, :4]  # Keep only x,y,z,intensity
            elif points.shape[0] % 4 == 0:  # KITTI format (4 dimensions)
                points = points.reshape(-1, 4)
            else:
                raise ValueError(f"Unexpected point cloud format with {points.shape[0]} total elements")
        elif self.ext == '.npy':
            points = np.load(self.sample_file_list[index])
            # Ensure we have 4 dimensions (x,y,z,intensity)
            if points.shape[1] > 4:
                points = points[:, :4]
        else:
            raise NotImplementedError

        input_dict = {
            'points': points,
            'frame_id': index,
        }

        data_dict = self.prepare_data(data_dict=input_dict)
        return data_dict


def parse_config():
    parser = argparse.ArgumentParser(description='arg parser')
    parser.add_argument('--cfg_file', type=str, default='cfgs/kitti_models/second.yaml',
                        help='specify the config for demo')
    parser.add_argument('--data_path', type=str, default='/home/<USER>/OpenPCDet/data/nuscenes/v1.0-mini',
                        help='specify the point cloud data file or directory')
    parser.add_argument('--ckpt', type=str, default=None, help='specify the pretrained model')
    parser.add_argument('--ext', type=str, default='.bin', help='specify the extension of your point cloud data file')
    parser.add_argument('--dataset_type', type=str, default='auto', choices=['auto', 'kitti', 'nuscenes'],
                        help='specify the dataset type to handle coordinate system correctly')

    args = parser.parse_args()

    cfg_from_yaml_file(args.cfg_file, cfg)

    return args, cfg


def detect_dataset_type(cfg_file, data_path):
    """Auto-detect dataset type based on config file and data path"""
    cfg_file_lower = cfg_file.lower()
    data_path_lower = str(data_path).lower()

    if 'nuscenes' in cfg_file_lower or 'nuscenes' in data_path_lower:
        return 'nuscenes'
    elif 'kitti' in cfg_file_lower or 'kitti' in data_path_lower:
        return 'kitti'
    else:
        return 'unknown'

def main():
    args, cfg = parse_config()
    logger = common_utils.create_logger()
    logger.info('-----------------Quick Demo of OpenPCDet-------------------------')

    # Auto-detect dataset type if not specified
    if args.dataset_type == 'auto':
        detected_type = detect_dataset_type(args.cfg_file, args.data_path)
        logger.info(f'Auto-detected dataset type: {detected_type}')
        dataset_type = detected_type
    else:
        dataset_type = args.dataset_type
        logger.info(f'Using specified dataset type: {dataset_type}')

    demo_dataset = DemoDataset(
        dataset_cfg=cfg.DATA_CONFIG, class_names=cfg.CLASS_NAMES, training=False,
        root_path=Path(args.data_path), ext=args.ext, logger=logger
    )
    logger.info(f'Total number of samples: \t{len(demo_dataset)}')

    model = build_network(model_cfg=cfg.MODEL, num_class=len(cfg.CLASS_NAMES), dataset=demo_dataset)
    model.load_params_from_file(filename=args.ckpt, logger=logger, to_cpu=True)
    model.cuda()
    model.eval()
    with torch.no_grad():
        for idx, data_dict in enumerate(demo_dataset):
            logger.info(f'Visualized sample index: \t{idx + 1}')
            data_dict = demo_dataset.collate_batch([data_dict])
            load_data_to_gpu(data_dict)
            pred_dicts, _ = model.forward(data_dict)

            # Extract points correctly - handle batch dimension
            points = data_dict['points']
            if points.dim() == 3:  # Batched: (batch_size, num_points, features)
                points_vis = points[0, :, 1:4]  # Take first batch, remove batch index, keep x,y,z
            else:  # Non-batched: (num_points, features)
                points_vis = points[:, 1:4] if points.shape[1] > 4 else points[:, :3]

            # Extract prediction boxes
            pred_boxes = pred_dicts[0]['pred_boxes']
            pred_scores = pred_dicts[0]['pred_scores']
            pred_labels = pred_dicts[0]['pred_labels']

            logger.info(f'Points shape: {points_vis.shape}, Boxes shape: {pred_boxes.shape}')

            V.draw_scenes(
                points=points_vis, ref_boxes=pred_boxes,
                ref_scores=pred_scores, ref_labels=pred_labels
            )

            if not OPEN3D_FLAG:
                mlab.show(stop=True)

    logger.info('Demo done.')


if __name__ == '__main__':
    main()
