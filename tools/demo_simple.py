import argparse
import glob
from pathlib import Path

try:
    import open3d
    from visual_utils import open3d_vis_utils as V
    OPEN3D_FLAG = True
except:
    import mayavi.mlab as mlab
    from visual_utils import visualize_utils as V
    OPEN3D_FLAG = False

import numpy as np
import torch

from pcdet.config import cfg, cfg_from_yaml_file
from pcdet.datasets import DatasetTemplate
from pcdet.models import build_network, load_data_to_gpu
from pcdet.utils import common_utils


class SimpleDemoDataset(DatasetTemplate):
    def __init__(self, dataset_cfg, class_names, training=False, root_path=None, logger=None, ext='.bin'):
        """
        简化的Demo数据集，避免复杂的数据集初始化问题
        """
        # 先设置基本属性，避免调用父类的复杂初始化
        self.dataset_cfg = dataset_cfg
        self.training = training
        self.class_names = class_names
        self.logger = logger
        self.root_path = root_path if root_path is not None else Path(dataset_cfg.DATA_PATH)
        
        # 手动设置必要的属性
        self.point_cloud_range = np.array(self.dataset_cfg.POINT_CLOUD_RANGE, dtype=np.float32)
        
        # 简化的初始化，避免复杂的数据处理器
        from pcdet.datasets.processor.point_feature_encoder import PointFeatureEncoder
        from pcdet.datasets.processor.data_processor import DataProcessor
        
        self.point_feature_encoder = PointFeatureEncoder(
            self.dataset_cfg.POINT_FEATURE_ENCODING,
            point_cloud_range=self.point_cloud_range
        )
        
        self.data_processor = DataProcessor(
            self.dataset_cfg.DATA_PROCESSOR, 
            point_cloud_range=self.point_cloud_range,
            training=self.training, 
            num_point_features=self.point_feature_encoder.num_point_features
        )
        
        self.grid_size = self.data_processor.grid_size
        self.voxel_size = self.data_processor.voxel_size
        self.total_epochs = 0
        self._merge_all_iters_to_one_epoch = False
        
        # 获取文件列表
        self.ext = ext
        data_file_list = glob.glob(str(root_path / f'*{self.ext}')) if self.root_path.is_dir() else [self.root_path]
        data_file_list.sort()
        self.sample_file_list = data_file_list

    @property
    def mode(self):
        return 'train' if self.training else 'test'

    def __len__(self):
        return len(self.sample_file_list)

    def __getitem__(self, index):
        if self.ext == '.bin':
            points = np.fromfile(self.sample_file_list[index], dtype=np.float32)
            # 处理不同维度的点云数据
            if points.shape[0] % 5 == 0:  # nuScenes format (5 dimensions)
                points = points.reshape(-1, 5)[:, :4]  # Keep only x,y,z,intensity
                print(f"Loaded nuScenes format: {points.shape[0]} points with 5D -> 4D")
            elif points.shape[0] % 4 == 0:  # KITTI format (4 dimensions)
                points = points.reshape(-1, 4)
                print(f"Loaded KITTI format: {points.shape[0]} points with 4D")
            else:
                raise ValueError(f"Unexpected point cloud format with {points.shape[0]} total elements")
        elif self.ext == '.npy':
            points = np.load(self.sample_file_list[index])
            if points.shape[1] > 4:
                points = points[:, :4]  # Keep only x, y, z, intensity
        else:
            raise NotImplementedError

        # 验证坐标系
        print(f"Point cloud range:")
        print(f"  X: [{points[:, 0].min():.2f}, {points[:, 0].max():.2f}]")
        print(f"  Y: [{points[:, 1].min():.2f}, {points[:, 1].max():.2f}]")
        print(f"  Z: [{points[:, 2].min():.2f}, {points[:, 2].max():.2f}]")

        input_dict = {
            'points': points,
            'frame_id': index,
        }

        data_dict = self.prepare_data(data_dict=input_dict)
        return data_dict

    def prepare_data(self, data_dict):
        """简化的数据准备，只做必要的处理"""
        if data_dict.get('points', None) is not None:
            data_dict = self.point_feature_encoder.forward(data_dict)

        data_dict = self.data_processor.forward(data_dict=data_dict)
        return data_dict

    @staticmethod
    def collate_batch(batch_list, _unused=False):
        """简化的batch整理"""
        from collections import defaultdict
        data_dict = defaultdict(list)
        for cur_sample in batch_list:
            for key, val in cur_sample.items():
                data_dict[key].append(val)
        
        ret = {}
        for key, val in data_dict.items():
            if key in ['points', 'voxel_coords']:
                coors = []
                for i, coor in enumerate(val):
                    coor_pad = np.pad(coor, ((0, 0), (1, 0)), mode='constant', constant_values=i)
                    coors.append(coor_pad)
                ret[key] = np.concatenate(coors, axis=0)
            elif key in ['voxels', 'voxel_num_points']:
                ret[key] = np.concatenate(val, axis=0)
            else:
                ret[key] = val[0] if len(val) == 1 else val
        
        return ret


def parse_config():
    parser = argparse.ArgumentParser(description='Simple Demo for OpenPCDet')
    parser.add_argument('--cfg_file', type=str, required=True,
                        help='specify the config for demo')
    parser.add_argument('--data_path', type=str, required=True,
                        help='specify the point cloud data file or directory')
    parser.add_argument('--ckpt', type=str, required=True, 
                        help='specify the pretrained model')
    parser.add_argument('--ext', type=str, default='.bin', 
                        help='specify the extension of your point cloud data file')
    parser.add_argument('--score_thresh', type=float, default=0.3,
                        help='score threshold for filtering predictions')
    parser.add_argument('--max_samples', type=int, default=5,
                        help='maximum number of samples to visualize')

    args = parser.parse_args()
    cfg_from_yaml_file(args.cfg_file, cfg)
    return args, cfg


def main():
    args, cfg = parse_config()
    logger = common_utils.create_logger()
    logger.info('-----------------Simple Demo of OpenPCDet-------------------------')
    
    demo_dataset = SimpleDemoDataset(
        dataset_cfg=cfg.DATA_CONFIG, class_names=cfg.CLASS_NAMES, training=False,
        root_path=Path(args.data_path), ext=args.ext, logger=logger
    )
    logger.info(f'Total number of samples: \t{len(demo_dataset)}')

    model = build_network(model_cfg=cfg.MODEL, num_class=len(cfg.CLASS_NAMES), dataset=demo_dataset)
    model.load_params_from_file(filename=args.ckpt, logger=logger, to_cpu=True)
    model.cuda()
    model.eval()
    
    with torch.no_grad():
        max_samples = min(args.max_samples, len(demo_dataset))
        for idx in range(max_samples):
            logger.info(f'Visualized sample index: \t{idx + 1}/{max_samples}')
            
            try:
                data_dict = demo_dataset[idx]
                data_dict = demo_dataset.collate_batch([data_dict])
                load_data_to_gpu(data_dict)
                
                pred_dicts, _ = model.forward(data_dict)
                
                # 提取预测结果
                pred_boxes = pred_dicts[0]['pred_boxes'].cpu().numpy()
                pred_scores = pred_dicts[0]['pred_scores'].cpu().numpy()
                pred_labels = pred_dicts[0]['pred_labels'].cpu().numpy()
                
                # 过滤低分预测
                mask = pred_scores > args.score_thresh
                pred_boxes = pred_boxes[mask]
                pred_scores = pred_scores[mask]
                pred_labels = pred_labels[mask]
                
                # 提取点云数据
                points = data_dict['points']
                if points.dim() == 2:  # (N, features)
                    points_vis = points[:, 1:4].cpu().numpy()  # 去掉batch index，保留x,y,z
                else:  # (batch, N, features)
                    points_vis = points[0, :, 1:4].cpu().numpy()
                
                logger.info(f'Found {len(pred_boxes)} predictions above threshold {args.score_thresh}')
                logger.info(f'Points shape: {points_vis.shape}')
                
                # 可视化
                V.draw_scenes(
                    points=points_vis, 
                    ref_boxes=pred_boxes,
                    ref_scores=pred_scores, 
                    ref_labels=pred_labels
                )
                
                if not OPEN3D_FLAG:
                    mlab.show(stop=True)
                else:
                    input("Press Enter to continue to next sample...")
                    
            except Exception as e:
                logger.error(f"Error processing sample {idx}: {e}")
                import traceback
                traceback.print_exc()
                continue

    logger.info('Demo done.')


if __name__ == '__main__':
    main()
