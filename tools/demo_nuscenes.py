import argparse
import numpy as np
import torch
import open3d as o3d
from pathlib import Path

from pcdet.config import cfg, cfg_from_yaml_file
from pcdet.datasets.nuscenes.nuscenes_dataset import NuScenesDataset
from pcdet.models import build_network, load_data_to_gpu
from pcdet.utils import common_utils
from visual_utils import open3d_vis_utils as V

def parse_args():
    parser = argparse.ArgumentParser(description="NuScenes Detection Demo")
    parser.add_argument('--cfg_file', type=str, required=True, help='Model config file')
    parser.add_argument('--ckpt', type=str, required=True, help='Checkpoint path')
    parser.add_argument('--data_root', type=str, required=True, help='NuScenes root dir')
    parser.add_argument('--info_path', type=str, default='nuscenes_infos_10sweeps_val.pkl',
                        help='NuScenes info pkl name (inside data_root)')
    parser.add_argument('--score_thresh', type=float, default=0.3, help='Score threshold')
    return parser.parse_args()

def main():
    args = parse_args()
    cfg_from_yaml_file(args.cfg_file, cfg)
    logger = common_utils.create_logger()
    logger.info('-----------------Quick Demo of OpenPCDet (NuScenes)-------------------------')

    logger.info("Loading NuScenes dataset")
    dataset_cfg = cfg.DATA_CONFIG
    # 修复：当training=False时，mode='test'，所以需要设置test的INFO_PATH
    dataset_cfg.INFO_PATH = {
        'test': [args.info_path],  # 为test模式设置info路径
        'val': [args.info_path]    # 保留val以防万一
    }

    dataset = NuScenesDataset(
        dataset_cfg=dataset_cfg,
        class_names=cfg.CLASS_NAMES,
        training=False,  # 这会导致mode='test'
        root_path=Path(args.data_root),
        logger=logger
    )


    logger.info(f"Total number of samples: {len(dataset)}")

    model = build_network(model_cfg=cfg.MODEL, num_class=len(cfg.CLASS_NAMES), dataset=dataset)
    model.load_params_from_file(filename=args.ckpt, logger=logger, to_cpu=True)
    model.cuda().eval()

    with torch.no_grad():
        for idx in range(min(10, len(dataset))):  # Limit to first 10 samples
            logger.info(f"Visualizing sample {idx + 1}/{min(10, len(dataset))}")

            try:
                data_dict = dataset[idx]
                logger.info(f"Original points shape: {data_dict['points'].shape}")

                data_dict = dataset.collate_batch([data_dict])
                load_data_to_gpu(data_dict)

                pred_dicts, _ = model.forward(data_dict)
                pred_boxes = pred_dicts[0]['pred_boxes'].cpu().numpy()
                pred_scores = pred_dicts[0]['pred_scores'].cpu().numpy()
                pred_labels = pred_dicts[0]['pred_labels'].cpu().numpy()

                logger.info(f"Raw predictions: {len(pred_boxes)} boxes")

                # Apply threshold
                mask = pred_scores > args.score_thresh
                pred_boxes = pred_boxes[mask]
                pred_scores = pred_scores[mask]
                pred_labels = pred_labels[mask]

                logger.info(f"Filtered predictions: {len(pred_boxes)} boxes above threshold {args.score_thresh}")

                # Extract points correctly
                points = data_dict['points'][0].cpu().numpy()[:, 1:4]  # (N, 3)

                logger.info(f"Points for visualization: {points.shape}")
                logger.info(f"Point cloud range:")
                logger.info(f"  X: [{points[:, 0].min():.2f}, {points[:, 0].max():.2f}]")
                logger.info(f"  Y: [{points[:, 1].min():.2f}, {points[:, 1].max():.2f}]")
                logger.info(f"  Z: [{points[:, 2].min():.2f}, {points[:, 2].max():.2f}]")

                if len(pred_boxes) > 0:
                    logger.info(f"Box center range:")
                    logger.info(f"  X: [{pred_boxes[:, 0].min():.2f}, {pred_boxes[:, 0].max():.2f}]")
                    logger.info(f"  Y: [{pred_boxes[:, 1].min():.2f}, {pred_boxes[:, 1].max():.2f}]")
                    logger.info(f"  Z: [{pred_boxes[:, 2].min():.2f}, {pred_boxes[:, 2].max():.2f}]")

                V.draw_scenes(
                    points=points,
                    ref_boxes=pred_boxes,
                    ref_scores=pred_scores,
                    ref_labels=pred_labels
                )

                input("Press Enter to view next frame...")

            except Exception as e:
                logger.error(f"Error processing sample {idx}: {e}")
                import traceback
                traceback.print_exc()
                continue

    logger.info("Demo done.")

if __name__ == '__main__':
    main()
