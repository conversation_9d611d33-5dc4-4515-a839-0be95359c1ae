import argparse
import glob
from pathlib import Path

try:
    import open3d
    from visual_utils import open3d_vis_utils as V
    OPEN3D_FLAG = True
except:
    import mayavi.mlab as mlab
    from visual_utils import visualize_utils as V
    OPEN3D_FLAG = False

import numpy as np
import torch

from pcdet.config import cfg, cfg_from_yaml_file
from pcdet.datasets import DatasetTemplate
from pcdet.models import build_network, load_data_to_gpu
from pcdet.utils import common_utils


class NuScenesCorrectDemo(DatasetTemplate):
    def __init__(self, dataset_cfg, class_names, training=False, root_path=None, logger=None, ext='.bin'):
        """
        完全按照nuScenes数据集的处理方式
        """
        super().__init__(
            dataset_cfg=dataset_cfg, class_names=class_names, training=training, root_path=root_path, logger=logger
        )
        self.ext = ext
        data_file_list = glob.glob(str(root_path / f'*{self.ext}')) if self.root_path.is_dir() else [self.root_path]
        data_file_list.sort()
        self.sample_file_list = data_file_list

    def __len__(self):
        return len(self.sample_file_list)

    def get_lidar_with_sweeps_demo(self, file_path, max_sweeps=1):
        """
        模拟nuScenes的get_lidar_with_sweeps方法，但只处理单个文件
        """
        # 按照nuScenes的方式读取点云：读取5维，取前4维
        points = np.fromfile(str(file_path), dtype=np.float32, count=-1).reshape([-1, 5])[:, :4]
        
        # 只有一个sweep，所以直接处理
        sweep_points_list = [points]
        sweep_times_list = [np.zeros((points.shape[0], 1))]
        
        # 如果需要多个sweeps，这里可以扩展
        # 但对于demo，我们只使用当前帧
        
        points = np.concatenate(sweep_points_list, axis=0)
        times = np.concatenate(sweep_times_list, axis=0).astype(points.dtype)
        
        # 重新添加时间戳，这是nuScenes的标准格式
        points = np.concatenate((points, times), axis=1)
        return points

    def __getitem__(self, index):
        file_path = self.sample_file_list[index]
        
        # 使用与nuScenes数据集相同的处理方式
        points = self.get_lidar_with_sweeps_demo(file_path, max_sweeps=1)
        
        print(f"Loaded file: {Path(file_path).name}")
        print(f"Points shape after nuScenes processing: {points.shape}")
        print(f"Point cloud range:")
        print(f"  X: [{points[:, 0].min():.2f}, {points[:, 0].max():.2f}]")
        print(f"  Y: [{points[:, 1].min():.2f}, {points[:, 1].max():.2f}]")
        print(f"  Z: [{points[:, 2].min():.2f}, {points[:, 2].max():.2f}]")
        print(f"  Intensity: [{points[:, 3].min():.2f}, {points[:, 3].max():.2f}]")
        print(f"  Timestamp: [{points[:, 4].min():.2f}, {points[:, 4].max():.2f}]")

        input_dict = {
            'points': points,
            'frame_id': Path(file_path).stem,
        }

        data_dict = self.prepare_data(data_dict=input_dict)
        return data_dict


def parse_config():
    parser = argparse.ArgumentParser(description='Correct nuScenes Demo for OpenPCDet')
    parser.add_argument('--cfg_file', type=str, required=True,
                        help='specify the config for demo')
    parser.add_argument('--data_path', type=str, required=True,
                        help='specify the point cloud data file or directory')
    parser.add_argument('--ckpt', type=str, required=True, 
                        help='specify the pretrained model')
    parser.add_argument('--ext', type=str, default='.bin', 
                        help='specify the extension of your point cloud data file')
    parser.add_argument('--score_thresh', type=float, default=0.3,
                        help='score threshold for filtering predictions')
    parser.add_argument('--max_samples', type=int, default=5,
                        help='maximum number of samples to visualize')

    args = parser.parse_args()
    cfg_from_yaml_file(args.cfg_file, cfg)
    return args, cfg


def main():
    args, cfg = parse_config()
    logger = common_utils.create_logger()
    logger.info('-----------------Correct nuScenes Demo of OpenPCDet-------------------------')
    
    demo_dataset = NuScenesCorrectDemo(
        dataset_cfg=cfg.DATA_CONFIG, class_names=cfg.CLASS_NAMES, training=False,
        root_path=Path(args.data_path), ext=args.ext, logger=logger
    )
    logger.info(f'Total number of samples: \t{len(demo_dataset)}')

    model = build_network(model_cfg=cfg.MODEL, num_class=len(cfg.CLASS_NAMES), dataset=demo_dataset)
    model.load_params_from_file(filename=args.ckpt, logger=logger, to_cpu=True)
    model.cuda()
    model.eval()
    
    with torch.no_grad():
        max_samples = min(args.max_samples, len(demo_dataset))
        for idx in range(max_samples):
            logger.info(f'Visualized sample index: \t{idx + 1}/{max_samples}')
            
            try:
                data_dict = demo_dataset[idx]
                data_dict = demo_dataset.collate_batch([data_dict])
                load_data_to_gpu(data_dict)
                
                pred_dicts, _ = model.forward(data_dict)
                
                # 提取预测结果
                pred_boxes = pred_dicts[0]['pred_boxes'].cpu().numpy()
                pred_scores = pred_dicts[0]['pred_scores'].cpu().numpy()
                pred_labels = pred_dicts[0]['pred_labels'].cpu().numpy()
                
                # 过滤低分预测
                mask = pred_scores > args.score_thresh
                pred_boxes = pred_boxes[mask]
                pred_scores = pred_scores[mask]
                pred_labels = pred_labels[mask]
                
                # 提取点云数据用于可视化
                points = data_dict['points']
                print(f"Raw points shape: {points.shape}")
                
                if points.dim() == 2:  # (N, features) - 已经包含batch index
                    # 去掉batch index (第0列)，保留x,y,z (第1,2,3列)
                    points_vis = points[:, 1:4].cpu().numpy()
                else:
                    points_vis = points[0, :, 1:4].cpu().numpy()
                
                logger.info(f'Found {len(pred_boxes)} predictions above threshold {args.score_thresh}')
                logger.info(f'Points for visualization: {points_vis.shape}')
                logger.info(f'Point cloud range for visualization:')
                logger.info(f'  X: [{points_vis[:, 0].min():.2f}, {points_vis[:, 0].max():.2f}]')
                logger.info(f'  Y: [{points_vis[:, 1].min():.2f}, {points_vis[:, 1].max():.2f}]')
                logger.info(f'  Z: [{points_vis[:, 2].min():.2f}, {points_vis[:, 2].max():.2f}]')
                
                if len(pred_boxes) > 0:
                    logger.info(f'Prediction boxes range:')
                    logger.info(f'  X: [{pred_boxes[:, 0].min():.2f}, {pred_boxes[:, 0].max():.2f}]')
                    logger.info(f'  Y: [{pred_boxes[:, 1].min():.2f}, {pred_boxes[:, 1].max():.2f}]')
                    logger.info(f'  Z: [{pred_boxes[:, 2].min():.2f}, {pred_boxes[:, 2].max():.2f}]')
                    
                    # 检查坐标范围是否合理
                    points_range_x = points_vis[:, 0].max() - points_vis[:, 0].min()
                    points_range_y = points_vis[:, 1].max() - points_vis[:, 1].min()
                    boxes_range_x = pred_boxes[:, 0].max() - pred_boxes[:, 0].min()
                    boxes_range_y = pred_boxes[:, 1].max() - pred_boxes[:, 1].min()
                    
                    logger.info(f'Coordinate range analysis:')
                    logger.info(f'  Points range - X: {points_range_x:.1f}m, Y: {points_range_y:.1f}m')
                    logger.info(f'  Boxes range - X: {boxes_range_x:.1f}m, Y: {boxes_range_y:.1f}m')
                    
                    # 检查是否在合理范围内
                    if (abs(points_vis[:, 0].min()) > 200 or abs(points_vis[:, 0].max()) > 200 or
                        abs(points_vis[:, 1].min()) > 200 or abs(points_vis[:, 1].max()) > 200):
                        logger.warning("Point cloud coordinates seem unusually large!")
                    
                    if (abs(pred_boxes[:, 0].min()) > 200 or abs(pred_boxes[:, 0].max()) > 200 or
                        abs(pred_boxes[:, 1].min()) > 200 or abs(pred_boxes[:, 1].max()) > 200):
                        logger.warning("Prediction box coordinates seem unusually large!")
                
                # 可视化
                V.draw_scenes(
                    points=points_vis, 
                    ref_boxes=pred_boxes,
                    ref_scores=pred_scores, 
                    ref_labels=pred_labels
                )
                
                if not OPEN3D_FLAG:
                    mlab.show(stop=True)
                else:
                    input("Press Enter to continue to next sample...")
                    
            except Exception as e:
                logger.error(f"Error processing sample {idx}: {e}")
                import traceback
                traceback.print_exc()
                continue

    logger.info('Demo done.')


if __name__ == '__main__':
    main()
