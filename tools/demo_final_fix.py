import argparse
import pickle
from pathlib import Path

try:
    import open3d
    from visual_utils import open3d_vis_utils as V
    OPEN3D_FLAG = True
except:
    import mayavi.mlab as mlab
    from visual_utils import visualize_utils as V
    OPEN3D_FLAG = False

import numpy as np
import torch

from pcdet.config import cfg, cfg_from_yaml_file
from pcdet.datasets import DatasetTemplate
from pcdet.models import build_network, load_data_to_gpu
from pcdet.utils import common_utils


class FinalFixDemo(DatasetTemplate):
    def __init__(self, dataset_cfg, class_names, info_path, training=False, root_path=None, logger=None):
        """
        最终修复版本：正确处理坐标系对齐问题
        """
        super().__init__(
            dataset_cfg=dataset_cfg, class_names=class_names, training=training, root_path=root_path, logger=logger
        )
        
        # 直接加载info文件
        with open(info_path, 'rb') as f:
            self.infos = pickle.load(f)
        
        logger.info(f'Loaded {len(self.infos)} samples from {info_path}')

    def __len__(self):
        return len(self.infos)

    def get_lidar_with_sweeps(self, index, max_sweeps=10):
        """
        完全按照nuScenes数据集的方式处理多帧数据
        """
        info = self.infos[index]
        lidar_path = self.root_path / info['lidar_path']
        
        # 读取主帧点云数据
        points = np.fromfile(str(lidar_path), dtype=np.float32, count=-1).reshape([-1, 5])[:, :4]
        
        sweep_points_list = [points]
        sweep_times_list = [np.zeros((points.shape[0], 1))]
        
        # 处理sweep数据（多帧融合）
        if 'sweeps' in info and len(info['sweeps']) > 0:
            num_sweeps = min(max_sweeps - 1, len(info['sweeps']))
            for k in range(num_sweeps):
                sweep_info = info['sweeps'][k]
                sweep_lidar_path = self.root_path / sweep_info['lidar_path']
                
                if sweep_lidar_path.exists():
                    points_sweep = np.fromfile(str(sweep_lidar_path), dtype=np.float32, count=-1).reshape([-1, 5])[:, :4]
                    
                    # 移除ego附近的点
                    def remove_ego_points(points, center_radius=1.0):
                        mask = ~((np.abs(points[:, 0]) < center_radius) & (np.abs(points[:, 1]) < center_radius))
                        return points[mask]
                    
                    points_sweep = remove_ego_points(points_sweep)
                    
                    # 应用变换矩阵（关键！）
                    if 'transform_matrix' in sweep_info and sweep_info['transform_matrix'] is not None:
                        points_sweep = points_sweep.T  # (4, N)
                        num_points = points_sweep.shape[1]
                        points_sweep[:3, :] = sweep_info['transform_matrix'].dot(
                            np.vstack((points_sweep[:3, :], np.ones(num_points)))
                        )[:3, :]
                        points_sweep = points_sweep.T  # (N, 4)
                    
                    sweep_points_list.append(points_sweep)
                    
                    # 时间戳
                    time_lag = sweep_info.get('time_lag', 0.0)
                    times_sweep = time_lag * np.ones((points_sweep.shape[0], 1))
                    sweep_times_list.append(times_sweep)
        
        # 合并所有点云
        points = np.concatenate(sweep_points_list, axis=0)
        times = np.concatenate(sweep_times_list, axis=0).astype(points.dtype)
        
        # 添加时间戳维度
        points = np.concatenate((points, times), axis=1)
        return points

    def __getitem__(self, index):
        info = self.infos[index]
        
        # 使用多帧融合获取点云
        points = self.get_lidar_with_sweeps(index, max_sweeps=self.dataset_cfg.MAX_SWEEPS)
        
        print(f"Sample {index}: {Path(info['lidar_path']).name}")
        print(f"Original points shape: {points.shape}")
        print(f"Original point cloud range:")
        print(f"  X: [{points[:, 0].min():.2f}, {points[:, 0].max():.2f}]")
        print(f"  Y: [{points[:, 1].min():.2f}, {points[:, 1].max():.2f}]")
        print(f"  Z: [{points[:, 2].min():.2f}, {points[:, 2].max():.2f}]")

        input_dict = {
            'points': points,
            'frame_id': Path(info['lidar_path']).stem,
            'metadata': {'token': info['token']}
        }

        data_dict = self.prepare_data(data_dict=input_dict)
        return data_dict


def parse_config():
    parser = argparse.ArgumentParser(description='Final Fix nuScenes Demo')
    parser.add_argument('--cfg_file', type=str, required=True,
                        help='specify the config for demo')
    parser.add_argument('--data_root', type=str, required=True,
                        help='specify the root path of nuScenes dataset')
    parser.add_argument('--info_path', type=str, required=True,
                        help='specify the info file path')
    parser.add_argument('--ckpt', type=str, required=True, 
                        help='specify the pretrained model')
    parser.add_argument('--score_thresh', type=float, default=0.3,
                        help='score threshold for filtering predictions')
    parser.add_argument('--max_samples', type=int, default=5,
                        help='maximum number of samples to visualize')
    parser.add_argument('--show_gt', action='store_true',
                        help='also show ground truth boxes')

    args = parser.parse_args()
    cfg_from_yaml_file(args.cfg_file, cfg)
    return args, cfg


def main():
    args, cfg = parse_config()
    logger = common_utils.create_logger()
    logger.info('-----------------Final Fix nuScenes Demo-------------------------')
    
    demo_dataset = FinalFixDemo(
        dataset_cfg=cfg.DATA_CONFIG, 
        class_names=cfg.CLASS_NAMES, 
        info_path=args.info_path,
        training=False,
        root_path=Path(args.data_root), 
        logger=logger
    )
    
    logger.info(f'Using MAX_SWEEPS: {cfg.DATA_CONFIG.MAX_SWEEPS}')
    logger.info(f'Point cloud range: {cfg.DATA_CONFIG.POINT_CLOUD_RANGE}')

    model = build_network(model_cfg=cfg.MODEL, num_class=len(cfg.CLASS_NAMES), dataset=demo_dataset)
    model.load_params_from_file(filename=args.ckpt, logger=logger, to_cpu=True)
    model.cuda()
    model.eval()
    
    with torch.no_grad():
        max_samples = min(args.max_samples, len(demo_dataset))
        for idx in range(max_samples):
            logger.info(f'Visualized sample index: \t{idx + 1}/{max_samples}')
            
            try:
                data_dict = demo_dataset[idx]
                # 获取原始info用于GT对比
                original_info = demo_dataset.infos[idx]

                data_dict = demo_dataset.collate_batch([data_dict])
                load_data_to_gpu(data_dict)
                
                pred_dicts, _ = model.forward(data_dict)
                
                # 提取预测结果
                pred_boxes = pred_dicts[0]['pred_boxes'].cpu().numpy()
                pred_scores = pred_dicts[0]['pred_scores'].cpu().numpy()
                pred_labels = pred_dicts[0]['pred_labels'].cpu().numpy()
                
                logger.info(f'Raw predictions: {len(pred_boxes)} boxes')
                logger.info(f'Score range: [{pred_scores.min():.3f}, {pred_scores.max():.3f}]')
                
                # 应用分数过滤
                score_mask = pred_scores > args.score_thresh
                pred_boxes = pred_boxes[score_mask]
                pred_scores = pred_scores[score_mask]
                pred_labels = pred_labels[score_mask]
                
                logger.info(f'After filtering (>{args.score_thresh}): {len(pred_boxes)} boxes')
                
                if len(pred_boxes) > 0:
                    logger.info(f'Filtered score range: [{pred_scores.min():.3f}, {pred_scores.max():.3f}]')
                    logger.info(f'Prediction box center range:')
                    logger.info(f'  X: [{pred_boxes[:, 0].min():.2f}, {pred_boxes[:, 0].max():.2f}]')
                    logger.info(f'  Y: [{pred_boxes[:, 1].min():.2f}, {pred_boxes[:, 1].max():.2f}]')
                    logger.info(f'  Z: [{pred_boxes[:, 2].min():.2f}, {pred_boxes[:, 2].max():.2f}]')
                
                # 提取点云数据用于可视化
                points = data_dict['points']
                if points.dim() == 2:  # (N, features)
                    points_vis = points[:, 1:4].cpu().numpy()
                else:
                    points_vis = points[0, :, 1:4].cpu().numpy()
                
                logger.info(f'Points for visualization: {points_vis.shape}')
                logger.info(f'Processed point cloud range:')
                logger.info(f'  X: [{points_vis[:, 0].min():.2f}, {points_vis[:, 0].max():.2f}]')
                logger.info(f'  Y: [{points_vis[:, 1].min():.2f}, {points_vis[:, 1].max():.2f}]')
                logger.info(f'  Z: [{points_vis[:, 2].min():.2f}, {points_vis[:, 2].max():.2f}]')
                
                # 处理GT boxes（如果需要显示）
                gt_boxes_vis = None
                if args.show_gt and 'gt_boxes' in original_info:
                    gt_boxes_original = original_info['gt_boxes']
                    gt_names = original_info['gt_names']
                    
                    # 过滤GT boxes：只保留在处理后点云范围内的boxes
                    pc_range = cfg.DATA_CONFIG.POINT_CLOUD_RANGE
                    valid_gt_mask = (
                        (gt_boxes_original[:, 0] >= pc_range[0]) & (gt_boxes_original[:, 0] <= pc_range[3]) &
                        (gt_boxes_original[:, 1] >= pc_range[1]) & (gt_boxes_original[:, 1] <= pc_range[4]) &
                        (gt_boxes_original[:, 2] >= pc_range[2]) & (gt_boxes_original[:, 2] <= pc_range[5])
                    )
                    
                    if np.any(valid_gt_mask):
                        gt_boxes_vis = gt_boxes_original[valid_gt_mask]
                        gt_names_vis = gt_names[valid_gt_mask]
                        
                        logger.info(f'GT boxes in processed range: {len(gt_boxes_vis)}/{len(gt_boxes_original)}')
                        logger.info(f'GT box center range:')
                        logger.info(f'  X: [{gt_boxes_vis[:, 0].min():.2f}, {gt_boxes_vis[:, 0].max():.2f}]')
                        logger.info(f'  Y: [{gt_boxes_vis[:, 1].min():.2f}, {gt_boxes_vis[:, 1].max():.2f}]')
                        logger.info(f'  Z: [{gt_boxes_vis[:, 2].min():.2f}, {gt_boxes_vis[:, 2].max():.2f}]')
                        logger.info(f'GT classes in range: {np.unique(gt_names_vis)}')
                    else:
                        logger.warning("No GT boxes in the processed point cloud range!")
                
                # 可视化
                V.draw_scenes(
                    points=points_vis, 
                    gt_boxes=gt_boxes_vis[:, :7] if gt_boxes_vis is not None else None,
                    ref_boxes=pred_boxes[:, :7] if len(pred_boxes) > 0 else None,
                    ref_scores=pred_scores if len(pred_boxes) > 0 else None, 
                    ref_labels=pred_labels if len(pred_boxes) > 0 else None
                )
                
                if not OPEN3D_FLAG:
                    mlab.show(stop=True)
                else:
                    input("Press Enter to continue to next sample...")
                    
            except Exception as e:
                logger.error(f"Error processing sample {idx}: {e}")
                import traceback
                traceback.print_exc()
                continue

    logger.info('Demo done.')


if __name__ == '__main__':
    main()
