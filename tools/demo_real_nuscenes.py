import argparse
import pickle
from pathlib import Path

try:
    import open3d
    from visual_utils import open3d_vis_utils as V
    OPEN3D_FLAG = True
except:
    import mayavi.mlab as mlab
    from visual_utils import visualize_utils as V
    OPEN3D_FLAG = False

import numpy as np
import torch

from pcdet.config import cfg, cfg_from_yaml_file
from pcdet.datasets.nuscenes.nuscenes_dataset import NuScenesDataset
from pcdet.models import build_network, load_data_to_gpu
from pcdet.utils import common_utils


def parse_config():
    parser = argparse.ArgumentParser(description='Real nuScenes Demo using actual dataset')
    parser.add_argument('--cfg_file', type=str, required=True,
                        help='specify the config for demo')
    parser.add_argument('--data_root', type=str, required=True,
                        help='specify the root path of nuScenes dataset')
    parser.add_argument('--info_path', type=str, required=True,
                        help='specify the info file path (e.g., nuscenes_infos_10sweeps_val.pkl)')
    parser.add_argument('--ckpt', type=str, required=True, 
                        help='specify the pretrained model')
    parser.add_argument('--score_thresh', type=float, default=0.3,
                        help='score threshold for filtering predictions')
    parser.add_argument('--max_samples', type=int, default=5,
                        help='maximum number of samples to visualize')

    args = parser.parse_args()
    cfg_from_yaml_file(args.cfg_file, cfg)
    return args, cfg


def main():
    args, cfg = parse_config()
    logger = common_utils.create_logger()
    logger.info('-----------------Real nuScenes Demo of OpenPCDet-------------------------')
    
    # 使用真实的nuScenes数据集，完全按照训练时的方式
    logger.info("Loading NuScenes dataset with real preprocessing...")
    dataset_cfg = cfg.DATA_CONFIG
    
    # 设置正确的info路径
    dataset_cfg.INFO_PATH = { 
        'test': [args.info_path]  # demo时使用test模式
    }
    dataset_cfg.DATA_PATH = args.data_root
    
    # 创建真实的nuScenes数据集
    dataset = NuScenesDataset(
        dataset_cfg=dataset_cfg,
        class_names=cfg.CLASS_NAMES,
        training=False,  # 这会使mode='test'
        root_path=Path(args.data_root),
        logger=logger
    )
    
    logger.info(f'Total number of samples: \t{len(dataset)}')
    logger.info(f'Using MAX_SWEEPS: {dataset_cfg.MAX_SWEEPS}')
    logger.info(f'Point cloud range: {dataset_cfg.POINT_CLOUD_RANGE}')

    model = build_network(model_cfg=cfg.MODEL, num_class=len(cfg.CLASS_NAMES), dataset=dataset)
    model.load_params_from_file(filename=args.ckpt, logger=logger, to_cpu=True)
    model.cuda()
    model.eval()
    
    with torch.no_grad():
        max_samples = min(args.max_samples, len(dataset))
        for idx in range(max_samples):
            logger.info(f'Visualized sample index: \t{idx + 1}/{max_samples}')
            
            try:
                # 使用真实数据集的__getitem__方法，包含完整的预处理
                data_dict = dataset[idx]
                
                # 显示原始数据信息
                logger.info(f"Frame ID: {data_dict.get('frame_id', 'unknown')}")
                if 'metadata' in data_dict:
                    logger.info(f"Token: {data_dict['metadata'].get('token', 'unknown')}")
                
                # 显示点云信息
                points = data_dict['points']
                logger.info(f"Points shape: {points.shape}")
                logger.info(f"Point features: {dataset_cfg.POINT_FEATURE_ENCODING.src_feature_list}")
                logger.info(f"Points range:")
                logger.info(f"  X: [{points[:, 0].min():.2f}, {points[:, 0].max():.2f}]")
                logger.info(f"  Y: [{points[:, 1].min():.2f}, {points[:, 1].max():.2f}]")
                logger.info(f"  Z: [{points[:, 2].min():.2f}, {points[:, 2].max():.2f}]")
                if points.shape[1] > 4:
                    logger.info(f"  Timestamp: [{points[:, 4].min():.2f}, {points[:, 4].max():.2f}]")
                
                # 准备batch数据
                data_dict = dataset.collate_batch([data_dict])
                load_data_to_gpu(data_dict)
                
                # 模型推理
                pred_dicts, _ = model.forward(data_dict)
                
                # 提取预测结果
                pred_boxes = pred_dicts[0]['pred_boxes'].cpu().numpy()
                pred_scores = pred_dicts[0]['pred_scores'].cpu().numpy()
                pred_labels = pred_dicts[0]['pred_labels'].cpu().numpy()
                
                logger.info(f'Raw predictions: {len(pred_boxes)} boxes')
                logger.info(f'Score range: [{pred_scores.min():.3f}, {pred_scores.max():.3f}]')
                
                # 应用分数过滤
                score_mask = pred_scores > args.score_thresh
                pred_boxes = pred_boxes[score_mask]
                pred_scores = pred_scores[score_mask]
                pred_labels = pred_labels[score_mask]
                
                logger.info(f'After filtering (>{args.score_thresh}): {len(pred_boxes)} boxes')
                
                if len(pred_boxes) > 0:
                    logger.info(f'Filtered score range: [{pred_scores.min():.3f}, {pred_scores.max():.3f}]')
                    logger.info(f'Box center range:')
                    logger.info(f'  X: [{pred_boxes[:, 0].min():.2f}, {pred_boxes[:, 0].max():.2f}]')
                    logger.info(f'  Y: [{pred_boxes[:, 1].min():.2f}, {pred_boxes[:, 1].max():.2f}]')
                    logger.info(f'  Z: [{pred_boxes[:, 2].min():.2f}, {pred_boxes[:, 2].max():.2f}]')
                    
                    # 显示类别分布
                    unique_labels, counts = np.unique(pred_labels, return_counts=True)
                    for label, count in zip(unique_labels, counts):
                        class_name = cfg.CLASS_NAMES[int(label) - 1]
                        logger.info(f'  {class_name}: {count} boxes')
                
                # 提取点云数据用于可视化
                points = data_dict['points']
                if points.dim() == 2:  # (N, features)
                    points_vis = points[:, 1:4].cpu().numpy()  # 去掉batch index，保留x,y,z
                else:
                    points_vis = points[0, :, 1:4].cpu().numpy()
                
                logger.info(f'Points for visualization: {points_vis.shape}')
                logger.info(f'Processed point cloud range:')
                logger.info(f'  X: [{points_vis[:, 0].min():.2f}, {points_vis[:, 0].max():.2f}]')
                logger.info(f'  Y: [{points_vis[:, 1].min():.2f}, {points_vis[:, 1].max():.2f}]')
                logger.info(f'  Z: [{points_vis[:, 2].min():.2f}, {points_vis[:, 2].max():.2f}]')
                
                # 可视化
                if len(pred_boxes) > 0:
                    V.draw_scenes(
                        points=points_vis, 
                        ref_boxes=pred_boxes,
                        ref_scores=pred_scores, 
                        ref_labels=pred_labels
                    )
                else:
                    logger.warning("No valid predictions to visualize!")
                    V.draw_scenes(points=points_vis)
                
                if not OPEN3D_FLAG:
                    mlab.show(stop=True)
                else:
                    input("Press Enter to continue to next sample...")
                    
            except Exception as e:
                logger.error(f"Error processing sample {idx}: {e}")
                import traceback
                traceback.print_exc()
                continue

    logger.info('Demo done.')


if __name__ == '__main__':
    main()
