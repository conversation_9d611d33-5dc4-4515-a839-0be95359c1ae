# nuScenes Demo 可视化问题修复说明

## 问题分析

你遇到的问题确实是由于坐标系不统一导致的，主要包括：

1. **点云数据维度差异**：
   - KITTI: 4维 (x, y, z, intensity)
   - nuScenes: 5维 (x, y, z, intensity, ring_index)

2. **数据处理错误**：
   - 原始demo.py在第100行使用 `data_dict['points'][:, 1:]` 去掉batch index，但可能导致坐标轴错位
   - 没有正确处理nuScenes的5维点云数据

3. **坐标系转换问题**：
   - 虽然KITTI和nuScenes理论上都使用相同的坐标系定义（x向前，y向左，z向上），但在实际数据处理中可能存在细微差异

## 修复内容

### 1. 修复了 `tools/demo.py`

**主要改进**：
- 自动检测点云数据格式（4维或5维）
- 正确处理nuScenes的5维数据，只保留前4维
- 改进了点云数据的提取逻辑
- 添加了数据集类型自动检测
- 增强了调试信息输出

**关键修改**：
```python
# 自动处理不同维度的点云数据
if points.shape[0] % 5 == 0:  # nuScenes format (5 dimensions)
    points = points.reshape(-1, 5)[:, :4]  # Keep only x,y,z,intensity
elif points.shape[0] % 4 == 0:  # KITTI format (4 dimensions)
    points = points.reshape(-1, 4)

# 正确提取可视化用的点云坐标
if points.dim() == 3:  # Batched: (batch_size, num_points, features)
    points_vis = points[0, :, 1:4]  # Take first batch, remove batch index, keep x,y,z
else:  # Non-batched
    points_vis = points[:, 1:4] if points.shape[1] > 4 else points[:, :3]
```

### 2. 改进了可视化工具

**`tools/visual_utils/open3d_vis_utils.py`**：
- 添加了详细的调试信息输出
- 改进了边界框转换函数
- 增强了错误处理

**`tools/visual_utils/visualize_utils.py`**：
- 同样添加了调试信息
- 改进了数据验证

### 3. 改进了 `tools/demo_nuscenes.py`

这是一个专门为nuScenes数据设计的demo脚本：
- 使用正确的nuScenes数据集类
- 添加了详细的调试信息
- 限制了样本数量以便调试
- 增强了错误处理

## 使用方法

### 方法1：使用简化的demo_simple.py（推荐）

这是最简单的方法，避免了复杂的数据集配置问题：

```bash
cd tools
python demo_simple.py \
    --cfg_file /home/<USER>/OpenPCDet/output/nuScenes_models/cbgs_voxel01_res3d_centerpoint_swintransformer_5_27/default/cbgs_voxel01_res3d_centerpoint_swintransformer_5_27.yaml \
    --data_path /path/to/your/nuscenes/lidar/files \
    --ckpt /home/<USER>/OpenPCDet/output/nuScenes_models/cbgs_voxel01_res3d_centerpoint_swintransformer_5_27/default/ckpt/checkpoint_epoch_30.pth \
    --ext .bin \
    --score_thresh 0.3 \
    --max_samples 5
```

### 方法2：使用修复后的demo_nuscenes.py

```bash
cd tools
python demo_nuscenes.py \
    --cfg_file /home/<USER>/OpenPCDet/output/nuScenes_models/cbgs_voxel01_res3d_centerpoint_swintransformer_5_27/default/cbgs_voxel01_res3d_centerpoint_swintransformer_5_27.yaml \
    --data_root /home/<USER>/OpenPCDet/data/nuscenes/v1.0-mini \
    --info_path nuscenes_infos_10sweeps_val.pkl \
    --ckpt /home/<USER>/OpenPCDet/output/nuScenes_models/cbgs_voxel01_res3d_centerpoint_swintransformer_5_27/default/ckpt/checkpoint_epoch_30.pth \
    --score_thresh 0.3
```

### 方法3：使用修复后的通用demo.py

```bash
cd tools
python demo.py \
    --cfg_file /home/<USER>/OpenPCDet/output/nuScenes_models/cbgs_voxel01_res3d_centerpoint_swintransformer_5_27/default/cbgs_voxel01_res3d_centerpoint_swintransformer_5_27.yaml \
    --data_path /path/to/your/nuscenes/lidar/data \
    --ckpt /home/<USER>/OpenPCDet/output/nuScenes_models/cbgs_voxel01_res3d_centerpoint_swintransformer_5_27/default/ckpt/checkpoint_epoch_30.pth \
    --ext .bin \
    --dataset_type nuscenes
```

## 调试信息

修复后的代码会输出详细的调试信息，包括：

1. **点云数据信息**：
   - 点云形状和维度
   - 坐标范围（X, Y, Z轴的最小值和最大值）

2. **预测框信息**：
   - 预测框数量
   - 框中心坐标范围
   - 过滤后的框数量

3. **数据格式检测**：
   - 自动检测是KITTI还是nuScenes格式
   - 显示数据转换过程

## 常见问题排查

### 1. 如果框仍然显示错乱

检查输出的调试信息：
- 点云坐标范围是否合理（通常X: 0-80m, Y: -40到40m, Z: -3到5m）
- 预测框坐标范围是否与点云坐标范围匹配
- 是否正确加载了nuScenes格式的配置文件

### 2. 如果没有检测到任何框

- 降低score_thresh阈值（如0.1）
- 检查模型是否正确加载
- 确认使用的是正确的nuScenes模型权重

### 3. 如果点云显示异常

- 检查点云文件是否为nuScenes格式（5维）
- 确认数据路径正确
- 查看调试信息中的点云形状

## 坐标系说明

OpenPCDet统一坐标系：
- X轴：指向前方（车辆行驶方向）
- Y轴：指向左侧
- Z轴：指向上方

这个坐标系对KITTI和nuScenes都适用，但在数据预处理阶段可能存在细微的转换差异。

## 下一步建议

1. 先使用修复后的demo.py测试几个样本
2. 观察调试信息，确认坐标范围是否合理
3. 如果问题仍然存在，可以进一步检查模型配置文件中的坐标系设置
4. 考虑使用nuScenes官方的可视化工具进行对比验证
