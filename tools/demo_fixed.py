import argparse
import glob
from pathlib import Path

try:
    import open3d
    from visual_utils import open3d_vis_utils as V
    OPEN3D_FLAG = True
except:
    import mayavi.mlab as mlab
    from visual_utils import visualize_utils as V
    OPEN3D_FLAG = False

import numpy as np
import torch

from pcdet.config import cfg, cfg_from_yaml_file
from pcdet.datasets import DatasetTemplate
from pcdet.models import build_network, load_data_to_gpu
from pcdet.utils import common_utils


class FixedDemoDataset(DatasetTemplate):
    def __init__(self, dataset_cfg, class_names, training=False, root_path=None, logger=None, ext='.bin'):
        """
        修复版本的Demo数据集，继承完整的DatasetTemplate功能
        """
        super().__init__(
            dataset_cfg=dataset_cfg, class_names=class_names, training=training, root_path=root_path, logger=logger
        )
        self.ext = ext
        data_file_list = glob.glob(str(root_path / f'*{self.ext}')) if self.root_path.is_dir() else [self.root_path]
        data_file_list.sort()
        self.sample_file_list = data_file_list

    def __len__(self):
        return len(self.sample_file_list)

    def __getitem__(self, index):
        if self.ext == '.bin':
            points = np.fromfile(self.sample_file_list[index], dtype=np.float32)
            # 处理不同维度的点云数据
            if points.shape[0] % 5 == 0:  # nuScenes format (5 dimensions)
                points = points.reshape(-1, 5)
                print(f"Loaded nuScenes format: {points.shape[0]} points with 5D")
                # 检查配置文件期望的特征数量
                expected_features = len(self.dataset_cfg.POINT_FEATURE_ENCODING.src_feature_list)
                if expected_features == 5:
                    # 配置期望5个特征，保持原样
                    pass
                elif expected_features == 4:
                    # 配置期望4个特征，去掉最后一维
                    points = points[:, :4]
                    print(f"Reduced to 4D as expected by config")
                else:
                    raise ValueError(f"Unexpected feature count in config: {expected_features}")
            elif points.shape[0] % 4 == 0:  # KITTI format (4 dimensions)
                points = points.reshape(-1, 4)
                print(f"Loaded KITTI format: {points.shape[0]} points with 4D")
                # 如果配置期望5个特征，需要添加时间戳
                expected_features = len(self.dataset_cfg.POINT_FEATURE_ENCODING.src_feature_list)
                if expected_features == 5:
                    # 添加时间戳列（全为0）
                    timestamp = np.zeros((points.shape[0], 1), dtype=np.float32)
                    points = np.concatenate([points, timestamp], axis=1)
                    print(f"Added timestamp column to match config expectation")
            else:
                raise ValueError(f"Unexpected point cloud format with {points.shape[0]} total elements")
        elif self.ext == '.npy':
            points = np.load(self.sample_file_list[index])
            expected_features = len(self.dataset_cfg.POINT_FEATURE_ENCODING.src_feature_list)
            if points.shape[1] > expected_features:
                points = points[:, :expected_features]
            elif points.shape[1] < expected_features:
                # 添加缺失的特征（用0填充）
                missing_features = expected_features - points.shape[1]
                padding = np.zeros((points.shape[0], missing_features), dtype=np.float32)
                points = np.concatenate([points, padding], axis=1)
        else:
            raise NotImplementedError

        # 验证坐标系
        print(f"Final points shape: {points.shape}")
        print(f"Point cloud range:")
        print(f"  X: [{points[:, 0].min():.2f}, {points[:, 0].max():.2f}]")
        print(f"  Y: [{points[:, 1].min():.2f}, {points[:, 1].max():.2f}]")
        print(f"  Z: [{points[:, 2].min():.2f}, {points[:, 2].max():.2f}]")

        input_dict = {
            'points': points,
            'frame_id': index,
        }

        data_dict = self.prepare_data(data_dict=input_dict)
        return data_dict


def parse_config():
    parser = argparse.ArgumentParser(description='Fixed Demo for OpenPCDet')
    parser.add_argument('--cfg_file', type=str, required=True,
                        help='specify the config for demo')
    parser.add_argument('--data_path', type=str, required=True,
                        help='specify the point cloud data file or directory')
    parser.add_argument('--ckpt', type=str, required=True, 
                        help='specify the pretrained model')
    parser.add_argument('--ext', type=str, default='.bin', 
                        help='specify the extension of your point cloud data file')
    parser.add_argument('--score_thresh', type=float, default=0.3,
                        help='score threshold for filtering predictions')
    parser.add_argument('--max_samples', type=int, default=5,
                        help='maximum number of samples to visualize')

    args = parser.parse_args()
    cfg_from_yaml_file(args.cfg_file, cfg)
    return args, cfg


def main():
    args, cfg = parse_config()
    logger = common_utils.create_logger()
    logger.info('-----------------Fixed Demo of OpenPCDet-------------------------')
    
    demo_dataset = FixedDemoDataset(
        dataset_cfg=cfg.DATA_CONFIG, class_names=cfg.CLASS_NAMES, training=False,
        root_path=Path(args.data_path), ext=args.ext, logger=logger
    )
    logger.info(f'Total number of samples: \t{len(demo_dataset)}')

    model = build_network(model_cfg=cfg.MODEL, num_class=len(cfg.CLASS_NAMES), dataset=demo_dataset)
    model.load_params_from_file(filename=args.ckpt, logger=logger, to_cpu=True)
    model.cuda()
    model.eval()
    
    with torch.no_grad():
        max_samples = min(args.max_samples, len(demo_dataset))
        for idx in range(max_samples):
            logger.info(f'Visualized sample index: \t{idx + 1}/{max_samples}')
            
            try:
                data_dict = demo_dataset[idx]
                data_dict = demo_dataset.collate_batch([data_dict])
                load_data_to_gpu(data_dict)
                
                pred_dicts, _ = model.forward(data_dict)
                
                # 提取预测结果
                pred_boxes = pred_dicts[0]['pred_boxes'].cpu().numpy()
                pred_scores = pred_dicts[0]['pred_scores'].cpu().numpy()
                pred_labels = pred_dicts[0]['pred_labels'].cpu().numpy()
                
                # 过滤低分预测
                mask = pred_scores > args.score_thresh
                pred_boxes = pred_boxes[mask]
                pred_scores = pred_scores[mask]
                pred_labels = pred_labels[mask]
                
                # 提取点云数据 - 修复坐标提取问题
                points = data_dict['points']
                print(f"Raw points shape: {points.shape}")
                
                if points.dim() == 2:  # (N, features) - 已经包含batch index
                    # 去掉batch index (第0列)，保留x,y,z (第1,2,3列)
                    points_vis = points[:, 1:4].cpu().numpy()
                else:  # 不应该出现这种情况，但以防万一
                    points_vis = points[0, :, 1:4].cpu().numpy()
                
                logger.info(f'Found {len(pred_boxes)} predictions above threshold {args.score_thresh}')
                logger.info(f'Points for visualization: {points_vis.shape}')
                logger.info(f'Point cloud range for visualization:')
                logger.info(f'  X: [{points_vis[:, 0].min():.2f}, {points_vis[:, 0].max():.2f}]')
                logger.info(f'  Y: [{points_vis[:, 1].min():.2f}, {points_vis[:, 1].max():.2f}]')
                logger.info(f'  Z: [{points_vis[:, 2].min():.2f}, {points_vis[:, 2].max():.2f}]')
                
                if len(pred_boxes) > 0:
                    logger.info(f'Prediction boxes range:')
                    logger.info(f'  X: [{pred_boxes[:, 0].min():.2f}, {pred_boxes[:, 0].max():.2f}]')
                    logger.info(f'  Y: [{pred_boxes[:, 1].min():.2f}, {pred_boxes[:, 1].max():.2f}]')
                    logger.info(f'  Z: [{pred_boxes[:, 2].min():.2f}, {pred_boxes[:, 2].max():.2f}]')
                
                # 可视化
                V.draw_scenes(
                    points=points_vis, 
                    ref_boxes=pred_boxes,
                    ref_scores=pred_scores, 
                    ref_labels=pred_labels
                )
                
                if not OPEN3D_FLAG:
                    mlab.show(stop=True)
                else:
                    input("Press Enter to continue to next sample...")
                    
            except Exception as e:
                logger.error(f"Error processing sample {idx}: {e}")
                import traceback
                traceback.print_exc()
                continue

    logger.info('Demo done.')


if __name__ == '__main__':
    main()
