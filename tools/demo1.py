import argparse
from pathlib import Path
import numpy as np
import torch

try:
    import open3d
    from visual_utils import open3d_vis_utils as V
    OPEN3D_FLAG = True
except:
    import mayavi.mlab as mlab
    from visual_utils import visualize_utils as V
    OPEN3D_FLAG = False

from pcdet.config import cfg, cfg_from_yaml_file
from pcdet.models import build_network, load_data_to_gpu
from pcdet.utils import common_utils
from pcdet.datasets.nuscenes.nuscenes_dataset import NuScenesDataset  # ✅ 用正式的 NuScenesDataset 替换 DemoDataset


def parse_config():
    parser = argparse.ArgumentParser(description='arg parser')
    parser.add_argument('--cfg_file', type=str, required=True, help='specify the config file')
    parser.add_argument('--ckpt', type=str, required=True, help='specify the pretrained model')
    parser.add_argument('--data_path', type=str, required=True, help='path to NuScenes root')
    parser.add_argument('--version', type=str, default='v1.0-mini', help='nuscenes version')
    parser.add_argument('--idx', type=int, default=0, help='sample index to visualize')
    parser.add_argument('--conf_thresh', type=float, default=0.3, help='confidence threshold for filtering detections')
    args = parser.parse_args()

    cfg_from_yaml_file(args.cfg_file, cfg)
    return args, cfg


def main():
    args, cfg = parse_config()
    logger = common_utils.create_logger()
    logger.info('--------------- NuScenes OpenPCDet Demo -----------------')

    # ✅ 修复 INFO_PATH 配置
    dataset_cfg = cfg.DATA_CONFIG
    # 为 test 模式设置正确的 info 文件路径
    dataset_cfg.INFO_PATH = {
        'test': ['nuscenes_infos_10sweeps_val.pkl'],  # 使用验证集作为测试
        'val': ['nuscenes_infos_10sweeps_val.pkl']
    }

    # ✅ 构造正式的 NuScenes 数据集
    # NuScenesDataset 会在 root_path 后面加上 VERSION，所以我们需要提供父目录
    data_root = Path(args.data_path).parent  # 从 /data/nuscenes/v1.0-mini 变成 /data/nuscenes
    dataset = NuScenesDataset(
        dataset_cfg=dataset_cfg,
        class_names=cfg.CLASS_NAMES,
        training=False,  # training=False 会使用 'test' 模式
        root_path=data_root,
        logger=logger
    )

    logger.info(f'Total number of samples: \t{len(dataset)}')
    
    # ✅ 构建模型并加载预训练权重
    model = build_network(model_cfg=cfg.MODEL, num_class=len(cfg.CLASS_NAMES), dataset=dataset)
    model.load_params_from_file(filename=args.ckpt, logger=logger, to_cpu=True)
    model.cuda()
    model.eval()

    with torch.no_grad():
        # ✅ 获取某一帧数据并封装
        data_dict = dataset[args.idx]
        logger.info(f'Visualizing index: {args.idx}, token: {data_dict.get("metadata", {}).get("token", "")}')
        data_dict = dataset.collate_batch([data_dict])
        load_data_to_gpu(data_dict)

        # ✅ 模型推理
        pred_dicts, _ = model.forward(data_dict)

        # ✅ 分析置信度分布
        pred_scores = pred_dicts[0]['pred_scores']
        pred_boxes = pred_dicts[0]['pred_boxes']
        pred_labels = pred_dicts[0]['pred_labels']

        print(f"Total predictions: {len(pred_scores)}")
        print(f"Score statistics:")
        print(f"  Min: {pred_scores.min():.4f}")
        print(f"  Max: {pred_scores.max():.4f}")
        print(f"  Mean: {pred_scores.mean():.4f}")
        print(f"  Median: {pred_scores.median():.4f}")

        # 显示不同阈值下的检测框数量
        for thresh in [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]:
            count = (pred_scores >= thresh).sum()
            print(f"  >= {thresh}: {count} boxes")

        # ✅ 应用置信度阈值过滤
        conf_thresh = args.conf_thresh  # 从命令行参数获取置信度阈值

        # 过滤低置信度的检测框
        mask = pred_scores >= conf_thresh
        filtered_boxes = pred_boxes[mask]
        filtered_scores = pred_scores[mask]
        filtered_labels = pred_labels[mask]

        print(f"\nAfter confidence filtering (>={conf_thresh}): {len(filtered_scores)}")
        if len(filtered_scores) > 0:
            print(f"Filtered score range: [{filtered_scores.min():.3f}, {filtered_scores.max():.3f}]")

            # 🔍 分析标签分布
            print(f"\nLabel distribution in filtered detections:")
            unique_labels, counts = torch.unique(filtered_labels, return_counts=True)
            for label, count in zip(unique_labels, counts):
                class_name = cfg.CLASS_NAMES[label] if label < len(cfg.CLASS_NAMES) else f"Unknown_{label}"
                print(f"  Label {label} ({class_name}): {count} boxes")
        else:
            print("No detections above threshold!")

        # ✅ 渲染可视化
        V.draw_scenes(
            points=data_dict['points'][:, 1:],  # 去掉 batch_idx
            ref_boxes=filtered_boxes,
            ref_scores=filtered_scores,
            ref_labels=filtered_labels
        )

        if not OPEN3D_FLAG:
            mlab.show(stop=True)

    logger.info('Demo done.')


if __name__ == '__main__':
    main()
