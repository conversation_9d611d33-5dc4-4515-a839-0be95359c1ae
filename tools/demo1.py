import argparse
from pathlib import Path
import numpy as np
import torch

try:
    import open3d
    from visual_utils import open3d_vis_utils as V
    OPEN3D_FLAG = True
except:
    import mayavi.mlab as mlab
    from visual_utils import visualize_utils as V
    OPEN3D_FLAG = False

from pcdet.config import cfg, cfg_from_yaml_file
from pcdet.models import build_network, load_data_to_gpu
from pcdet.utils import common_utils
from pcdet.datasets.nuscenes.nuscenes_dataset import NuScenesDataset  # ✅ 用正式的 NuScenesDataset 替换 DemoDataset


def parse_config():
    parser = argparse.ArgumentParser(description='arg parser')
    parser.add_argument('--cfg_file', type=str, required=True, help='specify the config file')
    parser.add_argument('--ckpt', type=str, required=True, help='specify the pretrained model')
    parser.add_argument('--data_path', type=str, required=True, help='path to NuScenes root')
    parser.add_argument('--version', type=str, default='v1.0-mini', help='nuscenes version')
    parser.add_argument('--idx', type=int, default=0, help='sample index to visualize')
    args = parser.parse_args()

    cfg_from_yaml_file(args.cfg_file, cfg)
    return args, cfg


def main():
    args, cfg = parse_config()
    logger = common_utils.create_logger()
    logger.info('--------------- NuScenes OpenPCDet Demo -----------------')

    # ✅ 构造正式的 NuScenes 数据集
    dataset = NuScenesDataset(
        dataset_cfg=cfg.DATA_CONFIG,
        class_names=cfg.CLASS_NAMES,
        training=False,
        root_path=Path(args.data_path),
        logger=logger
    )

    logger.info(f'Total number of samples: \t{len(dataset)}')
    
    # ✅ 构建模型并加载预训练权重
    model = build_network(model_cfg=cfg.MODEL, num_class=len(cfg.CLASS_NAMES), dataset=dataset)
    model.load_params_from_file(filename=args.ckpt, logger=logger, to_cpu=True)
    model.cuda()
    model.eval()

    with torch.no_grad():
        # ✅ 获取某一帧数据并封装
        data_dict = dataset[args.idx]
        logger.info(f'Visualizing index: {args.idx}, token: {data_dict.get("metadata", {}).get("token", "")}')
        data_dict = dataset.collate_batch([data_dict])
        load_data_to_gpu(data_dict)

        # ✅ 模型推理
        pred_dicts, _ = model.forward(data_dict)

        # ✅ 渲染可视化
        V.draw_scenes(
            points=data_dict['points'][:, 1:],  # 去掉 batch_idx
            ref_boxes=pred_dicts[0]['pred_boxes'],
            ref_scores=pred_dicts[0]['pred_scores'],
            ref_labels=pred_dicts[0]['pred_labels']
        )

        if not OPEN3D_FLAG:
            mlab.show(stop=True)

    logger.info('Demo done.')


if __name__ == '__main__':
    main()
