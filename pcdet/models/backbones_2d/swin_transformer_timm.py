import torch
import torch.nn as nn
from timm.models.swin_transformer import SwinTransformerBlock

class SwinTransformerTimmWrapper(nn.Module):

    def __init__(self, in_channels, out_channels):
        super().__init__()
        self.in_channels = in_channels
        self.out_channels = out_channels
        self.embed_dim = 96  # 标准嵌入维度

        # 特征投影层，将输入通道映射到嵌入维度
        self.feature_proj = nn.Sequential(
            nn.Conv2d(in_channels, self.embed_dim, kernel_size=1),
            nn.BatchNorm2d(self.embed_dim),
            nn.ReLU(inplace=True)
        )

        self.blocks = nn.ModuleList()

        self.window_size = 12

        for i in range(6):  # 层数
            # 偶数索引使用无位移窗口(shift_size=0)
            # 奇数索引使用有位移窗口(shift_size=window_size//2)
            shift_size = 0 if i % 2 == 0 else self.window_size // 2

            # 创建参数字典，以便根据需要添加或移除参数
            block_kwargs = {
                'dim': self.embed_dim,  # 特征维度
                'input_resolution': (128, 128),  # 初始分辨率（会在forward中动态调整）
                'num_heads': 8,  # 8个注意力头
                'window_size': self.window_size,  # 窗口大小设为13
                'mlp_ratio': 4.0,  # MLP比率
                'qkv_bias': True,  # 使用QKV偏置
                'attn_drop': 0.0,  # 注意力Dropout比率
                'drop_path': 0.1,  # 随机深度比率
                'norm_layer': nn.LayerNorm  # 归一化层
            }

            block_kwargs['proj_drop'] = 0.0  # 投影dropout比率
            block_kwargs['shift_size'] = shift_size  # 移位大小

            # 创建SwinTransformerBlock
            block = SwinTransformerBlock(**block_kwargs)
            self.blocks.append(block)

        # 输出投影层，将特征映射回原始通道数
        self.output_proj = nn.Sequential(
            nn.Conv2d(self.embed_dim, out_channels, kernel_size=1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.ReLU()
        )


    def forward(self, x):
        """
        通过SwinTransformer包装器的前向传播

        参数:
            x: 输入张量，形状为 [B, C, H, W]

        返回:
            增强的特征张量，形状为 [B, C, H, W]
        """
        # 将特征投影到嵌入维度
        x = self.feature_proj(x)  # [B, embed_dim, H, W]

        # 通过Swin Transformer块处理
        B, C, H, W = x.shape

        # SwinTransformerBlock期望输入形状为[B, H, W, C]
        x = x.permute(0, 2, 3, 1).contiguous()  # [B, H, W, C]

        # 更新每个块的输入分辨率
        # 这确保窗口划分和移位操作正确执行
        for block in self.blocks:
            # 动态设置输入分辨率
            block.input_resolution = (H, W)
            x = block(x)  # [B, H, W, C]

        # 将形状从[B, H, W, C]转换回[B, C, H, W]
        x = x.permute(0, 3, 1, 2).contiguous()  # [B, C, H, W]
        x = self.output_proj(x)  # [B, out_channels, H, W]

        output = x

        return output
