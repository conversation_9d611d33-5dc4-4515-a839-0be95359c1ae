import torch
import torch.nn as nn
import torch.nn.functional as F


# 窗口自注意力模块
class WindowAttention(nn.Module):
    def __init__(self, dim, window_size, num_heads):
        super().__init__()
        self.dim = dim
        self.window_size = window_size  # Wh, Ww
        self.num_heads = num_heads
        head_dim = dim // num_heads
        self.scale = head_dim ** -0.5

        # 相对位置编码
        self.relative_position_bias_table = nn.Parameter(
            torch.zeros((2 * window_size - 1) * (2 * window_size - 1), num_heads))

        # 获取窗口内每对位置的相对位置索引
        coords_h = torch.arange(self.window_size)
        coords_w = torch.arange(self.window_size)
        coords = torch.stack(torch.meshgrid([coords_h, coords_w], indexing="ij"))  # 2, Wh, Ww
        coords_flatten = torch.flatten(coords, 1)  # 2, Wh*Ww

        # 计算相对坐标
        relative_coords = coords_flatten[:, :, None] - coords_flatten[:, None, :]  # 2, Wh*Ww, Wh*Ww
        relative_coords = relative_coords.permute(1, 2, 0).contiguous()  # Wh*Ww, Wh*Ww, 2
        relative_coords[:, :, 0] += self.window_size - 1  # 偏移以使其为非负
        relative_coords[:, :, 1] += self.window_size - 1
        relative_coords[:, :, 0] *= 2 * self.window_size - 1
        relative_position_index = relative_coords.sum(-1)  # Wh*Ww, Wh*Ww
        self.register_buffer("relative_position_index", relative_position_index)

        self.qkv = nn.Linear(dim, dim * 3, bias=True)
        self.proj = nn.Linear(dim, dim)

        nn.init.trunc_normal_(self.relative_position_bias_table, std=.02)
        self.softmax = nn.Softmax(dim=-1)

    def forward(self, x):
        B_, N, C = x.shape  # B_: batch_size*num_windows, N: window_size*window_size

        # 生成qkv
        qkv = self.qkv(x).reshape(B_, N, 3, self.num_heads, C // self.num_heads).permute(2, 0, 3, 1, 4)
        q, k, v = qkv[0], qkv[1], qkv[2]  # B_, num_heads, N, C//num_heads

        # 计算注意力分数
        q = q * self.scale
        attn = (q @ k.transpose(-2, -1))  # B_, num_heads, N, N

        # 添加相对位置偏置
        relative_position_bias = self.relative_position_bias_table[self.relative_position_index.view(-1)].view(
            self.window_size * self.window_size, self.window_size * self.window_size, -1)  # Wh*Ww, Wh*Ww, num_heads
        relative_position_bias = relative_position_bias.permute(2, 0, 1).contiguous()  # num_heads, Wh*Ww, Wh*Ww
        attn = attn + relative_position_bias.unsqueeze(0)

        # 应用softmax
        attn = self.softmax(attn)

        # 计算加权和
        x = (attn @ v).transpose(1, 2).reshape(B_, N, C)
        x = self.proj(x)

        return x

# Swin Transformer块
class SwinTransformerBlock(nn.Module):
    def __init__(self, dim, num_heads, window_size, shift_size=0):
        super().__init__()
        self.dim = dim
        self.num_heads = num_heads
        self.window_size = window_size
        self.shift_size = shift_size

        self.norm1 = nn.LayerNorm(dim)
        self.attn = WindowAttention(dim, window_size, num_heads)
        self.norm2 = nn.LayerNorm(dim)
        self.mlp = nn.Sequential(
            nn.Linear(dim, 4 * dim),
            nn.GELU(),
            nn.Linear(4 * dim, dim)
        )

    def forward(self, x):
        B, C, H, W = x.shape
        shortcut = x

        # 将特征图从(B, C, H, W)转换为(B, H*W, C)
        x = x.flatten(2).transpose(1, 2)  # B, H*W, C

        # 第一个LayerNorm
        x = self.norm1(x)
        x = x.view(B, H, W, C)

        # 计算填充
        pad_h = (self.window_size - H % self.window_size) % self.window_size
        pad_w = (self.window_size - W % self.window_size) % self.window_size
        if pad_h > 0 or pad_w > 0:
            x = F.pad(x, (0, 0, 0, pad_w, 0, pad_h))

        _, Hp, Wp, _ = x.shape

        # 如果使用移位窗口
        if self.shift_size > 0:
            shifted_x = torch.roll(x, shifts=(-self.shift_size, -self.shift_size), dims=(1, 2))
        else:
            shifted_x = x

        # 将特征分割成窗口
        x_windows = self._window_partition(shifted_x, self.window_size)  # num_windows*B, window_size*window_size, C

        # 窗口自注意力
        attn_windows = self.attn(x_windows)  # num_windows*B, window_size*window_size, C

        # 将窗口合并回特征图
        shifted_x = self._window_reverse(attn_windows, self.window_size, Hp, Wp)  # B, Hp, Wp, C

        # 如果使用了移位窗口，需要反向移位
        if self.shift_size > 0:
            x = torch.roll(shifted_x, shifts=(self.shift_size, self.shift_size), dims=(1, 2))
        else:
            x = shifted_x

        # 如果有填充，需要移除
        if pad_h > 0 or pad_w > 0:
            x = x[:, :H, :W, :].contiguous()

        # 残差连接
        x = x.view(B, H * W, C)
        x = shortcut.flatten(2).transpose(1, 2) + x

        # FFN
        x = x + self.mlp(self.norm2(x))

        # 转换回(B, C, H, W)格式
        x = x.transpose(1, 2).view(B, C, H, W)

        return x

    def _window_partition(self, x, window_size):
        """将特征图分割成窗口"""
        B, H, W, C = x.shape
        x = x.view(B, H // window_size, window_size, W // window_size, window_size, C)
        windows = x.permute(0, 1, 3, 2, 4, 5).contiguous().view(-1, window_size * window_size, C)
        return windows

    def _window_reverse(self, windows, window_size, H, W):
        """将窗口合并回特征图"""
        B = int(windows.shape[0] / (H * W / window_size / window_size))
        x = windows.view(B, H // window_size, W // window_size, window_size, window_size, -1)
        x = x.permute(0, 1, 3, 2, 4, 5).contiguous().view(B, H, W, -1)
        return x

class SwinTransformerWrapper(nn.Module):
    def __init__(self, in_channels, out_channels):
        super().__init__()
        # Swin Transformer参数 - 增大窗口大小和嵌入维度
        self.in_channels = in_channels
        self.out_channels = out_channels
        self.embed_dim = 96  # 增加嵌入维度
        self.num_heads = 4  # 头数
        self.window_size = 8  # 增大窗口大小以增强感受野

        # 特征投影 - 不改变空间尺寸
        self.feature_proj = nn.Sequential(
            nn.Conv2d(in_channels, self.embed_dim, kernel_size=1),  # 1x1卷积
            nn.BatchNorm2d(self.embed_dim),
            nn.ReLU(inplace=True)
        )

        # 使用无位移和有位移的Swin Transformer块组合
        # 1. 无位移窗口 (W-MSA)
        self.transformer_block1 = SwinTransformerBlock(
            dim=self.embed_dim,
            num_heads=self.num_heads,
            window_size=self.window_size,
            shift_size=0  # 无位移窗口
        )

        # 2. 有位移窗口 (SW-MSA)
        self.transformer_block2 = SwinTransformerBlock(
            dim=self.embed_dim,
            num_heads=self.num_heads,
            window_size=self.window_size,
            shift_size=self.window_size // 2  # 有位移窗口
        )

        # 输出投影 - 映射回原始通道数
        self.output_proj = nn.Sequential(
            nn.Conv2d(self.embed_dim, out_channels, kernel_size=1, bias=False),
            nn.BatchNorm2d(out_channels),
            nn.ReLU()
        )

        # 通道注意力机制
        self.channel_attention = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(out_channels, out_channels // 16, kernel_size=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(out_channels // 16, out_channels, kernel_size=1),
            nn.Sigmoid()
        )

        # 保持低融合权重
        self.fusion_weight = nn.Parameter(torch.tensor(0.1))  # 给原始特征更高权重(0.9)

    def forward(self, x):
        x_in = x  # 保存输入特征用于残差连接

        # 特征投影
        x = self.feature_proj(x)  # B, embed_dim, H, W

        # 应用无位移窗口Transformer块 (W-MSA)
        x = self.transformer_block1(x)

        # 应用有位移窗口Transformer块 (SW-MSA)
        x = self.transformer_block2(x)

        # 输出投影
        x = self.output_proj(x)  # B, out_channels, H, W

        # 应用通道注意力
        channel_weights = self.channel_attention(x)
        x = x * channel_weights

        # 残差连接 - 使用低融合权重
        enhanced = self.fusion_weight * x
        output = enhanced + x_in  # 残差连接

        return output
